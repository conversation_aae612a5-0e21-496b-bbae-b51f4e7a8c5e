{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\system\\tasks\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\system\\tasks\\index.vue", "mtime": 1754292873038}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\babel.config.js", "mtime": 1754278696306}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754288923956}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/interopRequireWildcard.js\");\nvar _interopRequireDefault = require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/defineProperty.js\"));\nrequire(\"core-js/modules/es6.number.constructor\");\nrequire(\"core-js/modules/es6.regexp.to-string\");\nrequire(\"core-js/modules/es6.array.find\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.object.keys\");\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _tasks = _interopRequireDefault(require(\"@/api/system/tasks\"));\nvar _log = _interopRequireDefault(require(\"./log\"));\nvar _crud = _interopRequireWildcard(require(\"@crud/crud\"));\nvar _RR = _interopRequireDefault(require(\"@crud/RR.operation\"));\nvar _CRUD = _interopRequireDefault(require(\"@crud/CRUD.operation\"));\nvar _Pagination = _interopRequireDefault(require(\"@crud/Pagination\"));\nvar _DateRangePicker = _interopRequireDefault(require(\"@/components/DateRangePicker\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nvar defaultForm = {\n  id: 0,\n  taskGroup: null,\n  assemblyName: null,\n  taskName: null,\n  description: null,\n  className: null,\n  cron: null,\n  principal: null,\n  alertEmail: null,\n  startTime: null,\n  endTime: null,\n  triggerType: '1',\n  pauseAfterFailure: true,\n  isEnable: false,\n  runParams: null,\n  intervalSecond: null,\n  cycleRunTimes: null\n};\nvar _default = exports.default = {\n  name: 'Timing',\n  components: {\n    Log: _log.default,\n    pagination: _Pagination.default,\n    crudOperation: _CRUD.default,\n    rrOperation: _RR.default,\n    DateRangePicker: _DateRangePicker.default\n  },\n  cruds: function cruds() {\n    return (0, _crud.default)({\n      title: '作业调度',\n      url: 'api/tasks/query',\n      crudMethod: (0, _objectSpread2.default)({}, _tasks.default)\n    });\n  },\n  mixins: [(0, _crud.presenter)(), (0, _crud.header)(), (0, _crud.form)(defaultForm), (0, _crud.crud)()],\n  dicts: ['task_trigger_type'],\n  data: function data() {\n    return {\n      delLoading: false,\n      permission: {\n        add: ['timing_add'],\n        edit: ['timing_edit'],\n        del: ['timing_del'],\n        down: ['timing_down']\n      },\n      rules: {\n        taskGroup: [{\n          required: true,\n          message: '请输入任务組',\n          trigger: 'blur'\n        }],\n        assemblyName: [{\n          required: true,\n          message: '请输入程序集',\n          trigger: 'blur'\n        }],\n        taskName: [{\n          required: true,\n          message: '请输入任务名称',\n          trigger: 'blur'\n        }],\n        description: [{\n          required: true,\n          message: '请输入任务描述',\n          trigger: 'blur'\n        }],\n        className: [{\n          required: true,\n          message: '请输入执行类',\n          trigger: 'blur'\n        }],\n        principal: [{\n          required: true,\n          message: '请输入负责人名称',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  computed: {\n    isCronDisabled: function isCronDisabled() {\n      return this.form.triggerType === '0';\n    },\n    isSimpleDisabled: function isSimpleDisabled() {\n      return this.form.triggerType === '1';\n    }\n  },\n  methods: (0, _defineProperty2.default)((0, _defineProperty2.default)({\n    // 执行\n    execute: function execute(id) {\n      var _this = this;\n      _tasks.default.execution(id).then(function (res) {\n        _this.crud.message('执行成功', _crud.default.NOTIFICATION_TYPE.SUCCESS);\n        _this.crud.toQuery();\n      }).catch(function (err) {\n        console.log(err.response.data.message);\n      });\n    },\n    // 暂停\n    Pause: function Pause(id) {\n      var _this2 = this;\n      _tasks.default.pause(id).then(function (res) {\n        _this2.crud.message('暂停成功', _crud.default.NOTIFICATION_TYPE.SUCCESS);\n        _this2.crud.toQuery();\n      }).catch(function (err) {\n        console.log(err.response.data.message);\n      });\n    },\n    // 恢复\n    Resume: function Resume(id) {\n      var _this3 = this;\n      _tasks.default.resume(id).then(function (res) {\n        _this3.crud.message('恢复成功', _crud.default.NOTIFICATION_TYPE.SUCCESS);\n        _this3.crud.toQuery();\n      }).catch(function (err) {\n        console.log(err.response.data.message);\n      });\n    },\n    delMethod: function delMethod(id) {\n      var _this4 = this;\n      this.delLoading = true;\n      var idCollection = {\n        idArray: [id]\n      };\n      _tasks.default.del(idCollection).then(function () {\n        _this4.delLoading = false;\n        _this4.$refs[id].doClose();\n        _this4.crud.dleChangePage(1);\n        _this4.crud.delSuccessMessage();\n        _this4.crud.toQuery();\n      }).catch(function () {\n        _this4.delLoading = false;\n        _this4.$refs[id].doClose();\n      });\n    },\n    // 显示日志\n    doLog: function doLog(id) {\n      this.$refs.log.dialog = true;\n      this.$refs.log.doInit(id);\n    },\n    checkboxT: function checkboxT(row, rowIndex) {\n      return row.id !== 1;\n    },\n    getDictText: function getDictText(value) {\n      try {\n        var dictList = this.dict.task_trigger_type;\n        var keys = Object.keys(dictList);\n        var foundKey = keys.find(function (key) {\n          return dictList[key].value === value.toString();\n        });\n        if (foundKey) {\n          return dictList[foundKey].label;\n        }\n        return value;\n      } catch (err) {\n        return value;\n      }\n    },\n    handleRadioChange: function handleRadioChange() {\n      if (this.form.triggerType === '1') {\n        this.form.intervalSecond = null;\n        this.form.cycleRunTimes = null;\n      } else if (this.form.triggerType === '0') {\n        this.form.cron = null;\n      }\n    }\n  }, _crud.default.HOOK.afterValidateCU, function (crud) {\n    if (crud.form.triggerType === '1') {\n      if (crud.form.cron === null) {\n        this.$message({\n          message: 'cron模式下请设置作业执行cron表达式',\n          type: 'warning',\n          center: true\n        });\n        return false;\n      }\n    } else if (crud.form.triggerType === '0') {\n      if (crud.form.intervalSecond === null || crud.form.intervalSecond <= 0) {\n        this.$message({\n          message: 'simple模式下请设置作业间隔执行秒数',\n          type: 'warning',\n          center: true\n        });\n        return false;\n      }\n    }\n    if (crud.form.triggerType === '0') {\n      crud.form.intervalSecond = Number(crud.form.intervalSecond);\n    }\n    crud.form.triggerType = Number(crud.form.triggerType);\n    return true;\n  }), _crud.default.HOOK.afterToCU, function (crud) {\n    crud.form.triggerType = crud.form.triggerType.toString();\n  })\n};", null]}