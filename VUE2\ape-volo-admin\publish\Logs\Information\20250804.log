﻿
时间:2025-08-04 15:00:54.611
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using object serializer: Quartz.Simpl.BinaryObjectSerializer, Quartz

时间:2025-08-04 15:00:54.625
所在类:Quartz.Core.SchedulerSignalerImpl
等级:Information
信息:Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl

时间:2025-08-04 15:00:54.625
所在类:Quartz.Core.QuartzScheduler
等级:Information
信息:Quartz Scheduler created

时间:2025-08-04 15:00:54.625
所在类:Quartz.Simpl.RAMJobStore
等级:Information
信息:RAMJobStore initialized.

时间:2025-08-04 15:00:54.626
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Quartz Scheduler ******* - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized

时间:2025-08-04 15:00:54.626
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10

时间:2025-08-04 15:00:54.626
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False

时间:2025-08-04 15:00:54.875
所在类:Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager
等级:Information
信息:User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

时间:2025-08-04 15:01:00.083
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Now listening on: "http://[::]:8002"

时间:2025-08-04 15:01:00.085
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Application started. Press Ctrl+C to shut down.

时间:2025-08-04 15:01:00.085
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Hosting environment: "Production"

时间:2025-08-04 15:01:00.085
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Content root path: "D:\word\ape-volo\VUE2\ape-volo-admin\publish"

时间:2025-08-04 15:03:59.430
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:04:00.778
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:04:00.913
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:01.016
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:01.068
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 44.5897ms.

时间:2025-08-04 15:04:01.073
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:01.083
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 165.4804ms

时间:2025-08-04 15:04:01.084
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:04:01.102
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:04:01.104
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 1483.4390 ms

时间:2025-08-04 15:04:01.108
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 1683.7139ms

时间:2025-08-04 15:04:01.124
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:04:01.136
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:01.158
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:01.195
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 36.8478ms.

时间:2025-08-04 15:04:01.385
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:01.385
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 249.1917ms

时间:2025-08-04 15:04:01.385
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:04:01.387
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 283.6771 ms

时间:2025-08-04 15:04:01.387
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 285.7226ms

时间:2025-08-04 15:04:06.495
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "DELETE" "http"://"localhost:8002""""/auth/logout""" - null 0

时间:2025-08-04 15:04:06.500
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-04 15:04:06.503
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)"'

时间:2025-08-04 15:04:06.510
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Logout\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Logout()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:06.512
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:06.518
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 6.1602ms.

时间:2025-08-04 15:04:06.702
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:06.703
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)" in 192.4268ms

时间:2025-08-04 15:04:06.703
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)"'

时间:2025-08-04 15:04:06.703
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "DELETE" "/auth/logout" responded 200 in 207.8354 ms

时间:2025-08-04 15:04:06.703
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "DELETE" "http"://"localhost:8002""""/auth/logout""" - 200 108 "application/json; charset=utf-8" 208.3197ms

时间:2025-08-04 15:04:07.320
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - null null

时间:2025-08-04 15:04:07.354
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'

时间:2025-08-04 15:04:07.369
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Captcha\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Captcha()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:07.373
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:09.496
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 2123.1125ms.

时间:2025-08-04 15:04:09.497
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:09.497
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)" in 2127.3018ms

时间:2025-08-04 15:04:09.497
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'

时间:2025-08-04 15:04:09.497
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/captcha" responded 200 in 2150.6127 ms

时间:2025-08-04 15:04:09.497
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - 200 3472 "application/json; charset=utf-8" 2176.9912ms

时间:2025-08-04 15:04:11.425
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/login""" - "application/json" 273

时间:2025-08-04 15:04:11.425
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-04 15:04:11.426
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)"'

时间:2025-08-04 15:04:11.438
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Login\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Login(Ape.Volo.SharedModel.Queries.Login.LoginAuthUser)" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:11.469
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:11.480
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 11.4041ms.

时间:2025-08-04 15:04:11.636
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:11.637
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)" in 198.781ms

时间:2025-08-04 15:04:11.638
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)"'

时间:2025-08-04 15:04:11.638
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "POST" "/auth/login" responded 400 in 212.7742 ms

时间:2025-08-04 15:04:11.638
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/login""" - 400 121 "application/json; charset=utf-8" 213.3282ms

时间:2025-08-04 15:04:11.671
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - null null

时间:2025-08-04 15:04:11.673
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'

时间:2025-08-04 15:04:11.679
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Captcha\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Captcha()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:11.684
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 18.3537ms.

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)" in 24.1589ms

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'

时间:2025-08-04 15:04:11.703
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/captcha" responded 200 in 31.3134 ms

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - 200 3376 "application/json; charset=utf-8" 31.9843ms

时间:2025-08-04 15:04:14.444
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/login""" - "application/json" 272

时间:2025-08-04 15:04:14.445
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-04 15:04:14.445
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)"'

时间:2025-08-04 15:04:14.448
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Login\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Login(Ape.Volo.SharedModel.Queries.Login.LoginAuthUser)" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:14.452
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:14.968
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 516.7224ms.

时间:2025-08-04 15:04:15.105
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:15.105
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)" in 656.3523ms

时间:2025-08-04 15:04:15.105
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)"'

时间:2025-08-04 15:04:15.105
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "POST" "/auth/login" responded 200 in 660.5882 ms

时间:2025-08-04 15:04:15.105
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/login""" - 200 2365 "application/json; charset=utf-8" 661.1252ms

时间:2025-08-04 15:04:15.178
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:04:15.195
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:04:15.202
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:15.202
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:15.207
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 4.4686ms.

时间:2025-08-04 15:04:15.352
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:15.352
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 150.144ms

时间:2025-08-04 15:04:15.352
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:04:15.353
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 173.8988 ms

时间:2025-08-04 15:04:15.353
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 174.4421ms

时间:2025-08-04 15:04:19.719
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - null null

时间:2025-08-04 15:04:19.723
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - null null

时间:2025-08-04 15:04:19.726
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:19.726
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:19.741
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:19.741
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:19.755
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:19.759
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:19.879
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 120.7581ms.

时间:2025-08-04 15:04:19.909
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 154.6331ms.

时间:2025-08-04 15:04:20.038
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:20.038
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 297.0846ms

时间:2025-08-04 15:04:20.038
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:20.039
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 315.0112 ms

时间:2025-08-04 15:04:20.039
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - 200 449 "application/json; charset=utf-8" 319.633ms

时间:2025-08-04 15:04:20.297
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:20.297
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 555.5596ms

时间:2025-08-04 15:04:20.297
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:20.297
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 573.2586 ms

时间:2025-08-04 15:04:20.297
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - 200 304 "application/json; charset=utf-8" 574.5988ms

时间:2025-08-04 15:04:26.088
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:26.092
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:26.096
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:26.103
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:26.108
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.2822ms.

时间:2025-08-04 15:04:26.704
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:26.705
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 608.1858ms

时间:2025-08-04 15:04:26.705
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:26.705
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 616.1138 ms

时间:2025-08-04 15:04:26.705
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 617.0143ms

时间:2025-08-04 15:04:27.275
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59" - null null

时间:2025-08-04 15:04:27.277
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:27.283
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:27.284
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:27.290
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 6.0845ms.

时间:2025-08-04 15:04:27.440
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:27.441
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 157.7086ms

时间:2025-08-04 15:04:27.441
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:27.441
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 165.4223 ms

时间:2025-08-04 15:04:27.441
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59" - 200 304 "application/json; charset=utf-8" 165.9656ms

时间:2025-08-04 15:04:29.060
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=true" - null null

时间:2025-08-04 15:04:29.062
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:29.073
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:29.074
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:29.080
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.8604ms.

时间:2025-08-04 15:04:29.223
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:29.223
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 149.6394ms

时间:2025-08-04 15:04:29.223
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:29.223
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 162.9419 ms

时间:2025-08-04 15:04:29.224
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=true" - 200 304 "application/json; charset=utf-8" 163.4782ms

时间:2025-08-04 15:04:30.993
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - null null

时间:2025-08-04 15:04:30.995
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:31.000
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:31.002
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:31.005
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 3.1507ms.

时间:2025-08-04 15:04:31.132
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:31.132
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 131.5401ms

时间:2025-08-04 15:04:31.134
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:31.134
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 140.6255 ms

时间:2025-08-04 15:04:31.134
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - 200 1110 "application/json; charset=utf-8" 141.102ms

时间:2025-08-04 15:04:34.343
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "PUT" "http"://"localhost:8002""""/api/dept/edit""" - "application/json" 266

时间:2025-08-04 15:04:34.344
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-04 15:04:34.346
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)"'

时间:2025-08-04 15:04:34.355
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Update\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Update(Ape.Volo.SharedModel.Dto.Core.Permission.CreateUpdateDepartmentDto)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:34.363
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:34.634
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.NoContentResult" in 271.0152ms.

时间:2025-08-04 15:04:34.796
所在类:Microsoft.AspNetCore.Mvc.StatusCodeResult
等级:Information
信息:Executing StatusCodeResult, setting HTTP status code 204

时间:2025-08-04 15:04:34.797
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)" in 441.1276ms

时间:2025-08-04 15:04:34.797
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)"'

时间:2025-08-04 15:04:34.797
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "PUT" "/api/dept/edit" responded 204 in 453.3090 ms

时间:2025-08-04 15:04:34.797
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "PUT" "http"://"localhost:8002""""/api/dept/edit""" - 204 null null 453.8629ms

时间:2025-08-04 15:04:35.144
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59" - null null

时间:2025-08-04 15:04:35.150
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:35.163
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:35.163
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:35.175
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 11.4055ms.

时间:2025-08-04 15:04:35.324
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:35.324
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 161.1882ms

时间:2025-08-04 15:04:35.324
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:35.324
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 180.0699 ms

时间:2025-08-04 15:04:35.325
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59" - 200 304 "application/json; charset=utf-8" 180.8895ms

时间:2025-08-04 15:04:36.943
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:36.945
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:36.948
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:36.949
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:36.954
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.7608ms.

时间:2025-08-04 15:04:37.086
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:37.086
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 138.1663ms

时间:2025-08-04 15:04:37.086
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:37.087
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 142.9992 ms

时间:2025-08-04 15:04:37.087
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 143.4745ms

时间:2025-08-04 15:04:46.327
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:46.329
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:46.335
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:46.336
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:46.341
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 4.6713ms.

时间:2025-08-04 15:04:46.458
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:46.459
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 123.4548ms

时间:2025-08-04 15:04:46.459
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:46.459
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 132.1238 ms

时间:2025-08-04 15:04:46.459
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 132.5981ms

时间:2025-08-04 15:04:53.490
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:53.493
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:53.497
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:53.498
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:53.507
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 9.0145ms.

时间:2025-08-04 15:04:53.678
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:53.679
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 181.4117ms

时间:2025-08-04 15:04:53.679
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:53.679
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 188.4514 ms

时间:2025-08-04 15:04:53.679
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 189.0896ms

时间:2025-08-04 15:04:57.777
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:57.780
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:57.786
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:57.787
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:57.792
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.6818ms.

时间:2025-08-04 15:04:57.920
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:57.920
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 134.6205ms

时间:2025-08-04 15:04:57.921
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:57.921
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 143.0460 ms

时间:2025-08-04 15:04:57.921
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 143.4501ms

时间:2025-08-04 15:04:59.782
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:59.784
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:59.790
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:59.791
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:59.797
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.5094ms.

时间:2025-08-04 15:04:59.981
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:59.981
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 190.8148ms

时间:2025-08-04 15:04:59.981
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:59.982
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 199.3187 ms

时间:2025-08-04 15:04:59.982
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 199.9664ms

时间:2025-08-04 15:05:15.053
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using object serializer: Quartz.Simpl.BinaryObjectSerializer, Quartz

时间:2025-08-04 15:05:15.063
所在类:Quartz.Core.SchedulerSignalerImpl
等级:Information
信息:Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl

时间:2025-08-04 15:05:15.063
所在类:Quartz.Core.QuartzScheduler
等级:Information
信息:Quartz Scheduler created

时间:2025-08-04 15:05:15.064
所在类:Quartz.Simpl.RAMJobStore
等级:Information
信息:RAMJobStore initialized.

时间:2025-08-04 15:05:15.064
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Quartz Scheduler ******* - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized

时间:2025-08-04 15:05:15.064
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10

时间:2025-08-04 15:05:15.064
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False

时间:2025-08-04 15:05:15.268
所在类:Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager
等级:Information
信息:User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

时间:2025-08-04 15:05:15.416
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Now listening on: "http://[::]:8002"

时间:2025-08-04 15:05:15.418
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Application started. Press Ctrl+C to shut down.

时间:2025-08-04 15:05:15.419
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Hosting environment: "Production"

时间:2025-08-04 15:05:15.419
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Content root path: "D:\word\ape-volo\VUE2\ape-volo-admin\publish"

时间:2025-08-04 15:06:43.865
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:06:45.486
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:06:45.609
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:45.700
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:45.759
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 55.7541ms.

时间:2025-08-04 15:06:45.764
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:45.775
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 161.9668ms

时间:2025-08-04 15:06:45.775
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:06:45.795
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:06:45.825
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 1832.3750 ms

时间:2025-08-04 15:06:45.827
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:06:45.830
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 1995.609ms

时间:2025-08-04 15:06:45.835
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:45.849
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:45.880
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 30.6282ms.

时间:2025-08-04 15:06:46.095
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:46.096
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 260.7156ms

时间:2025-08-04 15:06:46.096
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:06:46.097
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 301.2320 ms

时间:2025-08-04 15:06:46.098
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 303.2449ms

时间:2025-08-04 15:06:47.893
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - null null

时间:2025-08-04 15:06:47.895
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - null null

时间:2025-08-04 15:06:47.900
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:06:47.909
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:47.912
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:47.941
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:47.948
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:47.969
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:47.970
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:47.996
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:06:47.996
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 96.0477 ms

时间:2025-08-04 15:06:47.996
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 96.8201ms

时间:2025-08-04 15:06:48.049
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 79.875ms.

时间:2025-08-04 15:06:48.077
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 106.9677ms.

时间:2025-08-04 15:06:48.170
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:48.175
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 226.7232ms

时间:2025-08-04 15:06:48.175
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:48.176
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 273.9022 ms

时间:2025-08-04 15:06:48.176
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - 200 449 "application/json; charset=utf-8" 282.3957ms

时间:2025-08-04 15:06:48.412
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:48.413
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 471.4695ms

时间:2025-08-04 15:06:48.413
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:48.413
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 510.7616 ms

时间:2025-08-04 15:06:48.413
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - 200 304 "application/json; charset=utf-8" 518.1696ms

时间:2025-08-04 15:06:52.461
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - null null

时间:2025-08-04 15:06:52.466
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:52.469
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:52.471
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:52.480
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 9.6798ms.

时间:2025-08-04 15:06:52.611
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:52.612
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 141.9931ms

时间:2025-08-04 15:06:52.612
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:52.612
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 150.3712 ms

时间:2025-08-04 15:06:52.612
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - 200 47 "application/json; charset=utf-8" 151.4344ms

时间:2025-08-04 15:06:57.904
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - null null

时间:2025-08-04 15:06:57.908
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:57.914
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:57.915
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:57.920
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.1221ms.

时间:2025-08-04 15:06:58.064
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:58.064
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 149.9945ms

时间:2025-08-04 15:06:58.064
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:58.064
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 159.5574 ms

时间:2025-08-04 15:06:58.064
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - 200 47 "application/json; charset=utf-8" 160.3001ms

时间:2025-08-04 15:07:02.861
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=true" - null null

时间:2025-08-04 15:07:02.863
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:02.869
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:02.869
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:02.877
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 7.4514ms.

时间:2025-08-04 15:07:03.028
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:03.028
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 159.4309ms

时间:2025-08-04 15:07:03.028
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:03.028
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 167.5754 ms

时间:2025-08-04 15:07:03.029
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=true" - 200 304 "application/json; charset=utf-8" 168.0212ms

时间:2025-08-04 15:07:05.086
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - null null

时间:2025-08-04 15:07:05.090
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:05.095
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:05.095
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:05.099
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 3.7849ms.

时间:2025-08-04 15:07:05.216
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:05.217
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 121.8482ms

时间:2025-08-04 15:07:05.217
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:05.217
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 130.9886 ms

时间:2025-08-04 15:07:05.217
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - 200 1133 "application/json; charset=utf-8" 131.536ms

时间:2025-08-04 15:07:09.666
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - null null

时间:2025-08-04 15:07:09.668
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:09.671
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:09.678
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:09.683
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 4.3218ms.

时间:2025-08-04 15:07:09.818
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:09.819
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 145.8106ms

时间:2025-08-04 15:07:09.819
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:09.819
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 152.8604 ms

时间:2025-08-04 15:07:09.819
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - 200 304 "application/json; charset=utf-8" 153.2841ms

时间:2025-08-04 15:07:11.499
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - null null

时间:2025-08-04 15:07:11.501
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:11.508
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:11.508
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:11.511
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 2.6554ms.

时间:2025-08-04 15:07:11.651
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:11.651
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 142.8434ms

时间:2025-08-04 15:07:11.651
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:11.651
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 151.7537 ms

时间:2025-08-04 15:07:11.651
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - 200 1133 "application/json; charset=utf-8" 152.3402ms

时间:2025-08-04 15:07:15.190
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/superior""?id=***************" - null null

时间:2025-08-04 15:07:15.193
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)"'

时间:2025-08-04 15:07:15.200
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"GetSuperior\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetSuperior(Int64)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:15.200
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:15.237
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 36.845ms.

时间:2025-08-04 15:07:15.549
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:15.549
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)" in 348.7819ms

时间:2025-08-04 15:07:15.549
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)"'

时间:2025-08-04 15:07:15.549
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/superior" responded 200 in 358.8743 ms

时间:2025-08-04 15:07:15.549
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/superior""?id=***************" - 200 1070 "application/json; charset=utf-8" 359.4157ms

时间:2025-08-04 15:08:50.927
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using object serializer: Quartz.Simpl.BinaryObjectSerializer, Quartz

时间:2025-08-04 15:08:50.943
所在类:Quartz.Core.SchedulerSignalerImpl
等级:Information
信息:Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl

时间:2025-08-04 15:08:50.946
所在类:Quartz.Core.QuartzScheduler
等级:Information
信息:Quartz Scheduler created

时间:2025-08-04 15:08:50.948
所在类:Quartz.Simpl.RAMJobStore
等级:Information
信息:RAMJobStore initialized.

时间:2025-08-04 15:08:50.950
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Quartz Scheduler ******* - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized

时间:2025-08-04 15:08:50.951
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10

时间:2025-08-04 15:08:50.953
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False

时间:2025-08-04 15:08:51.153
所在类:Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager
等级:Information
信息:User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

时间:2025-08-04 15:08:51.299
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Now listening on: "http://[::]:8002"

时间:2025-08-04 15:08:51.303
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Application started. Press Ctrl+C to shut down.

时间:2025-08-04 15:08:51.305
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Hosting environment: "Production"

时间:2025-08-04 15:08:51.318
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Content root path: "D:\word\ape-volo\VUE2\ape-volo-admin\publish"

时间:2025-08-04 15:09:00.479
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - null null

时间:2025-08-04 15:09:01.371
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:09:01.539
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:09:01.575
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:09:01.674
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 90.9212ms.

时间:2025-08-04 15:09:01.903
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:09:01.917
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 367.8475ms

时间:2025-08-04 15:09:01.944
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:09:01.958
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 1414.6587 ms

时间:2025-08-04 15:09:01.981
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - 200 47 "application/json; charset=utf-8" 1505.8073ms

时间:2025-08-04 15:09:24.419
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - null null

时间:2025-08-04 15:09:24.458
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:09:24.470
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:09:24.475
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:09:24.499
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 17.1328ms.

时间:2025-08-04 15:09:24.665
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:09:24.669
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 195.8983ms

时间:2025-08-04 15:09:24.687
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:09:24.689
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 263.5550 ms

时间:2025-08-04 15:09:24.697
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - 200 47 "application/json; charset=utf-8" 274.2925ms

时间:2025-08-04 15:09:52.167
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:09:52.286
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:09:52.308
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:09:52.416
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:09:52.543
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 124.4099ms.

时间:2025-08-04 15:09:52.553
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:09:52.554
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 217.4273ms

时间:2025-08-04 15:09:52.559
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:09:52.626
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:09:52.657
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 381.0620 ms

时间:2025-08-04 15:09:52.666
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 498.6621ms

时间:2025-08-04 15:09:52.667
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:09:52.683
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:09:52.719
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:09:52.750
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 26.9837ms.

时间:2025-08-04 15:09:52.894
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:09:52.898
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 211.1578ms

时间:2025-08-04 15:09:52.903
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:09:52.938
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 278.6161 ms

时间:2025-08-04 15:09:53.291
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 664.9199ms

时间:2025-08-04 15:09:53.873
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:09:53.886
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - null null

时间:2025-08-04 15:09:53.891
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - null null

时间:2025-08-04 15:09:54.025
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:09:54.074
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:09:54.232
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:09:54.238
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:09:54.247
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:09:54.274
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:09:54.289
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 283.1763 ms

时间:2025-08-04 15:09:54.294
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 421.7089ms

时间:2025-08-04 15:09:54.307
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:09:54.322
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 46.0656ms.

时间:2025-08-04 15:09:54.366
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 55.923ms.

时间:2025-08-04 15:09:54.657
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:09:54.673
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 399.4335ms

时间:2025-08-04 15:09:54.706
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:09:54.719
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 650.7281 ms

时间:2025-08-04 15:09:54.874
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - 200 304 "application/json; charset=utf-8" 983.2684ms

时间:2025-08-04 15:09:54.936
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:09:54.939
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 649.7217ms

时间:2025-08-04 15:09:54.969
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:09:54.972
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 952.1806 ms

时间:2025-08-04 15:09:54.976
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - 200 449 "application/json; charset=utf-8" 1089.9258ms

时间:2025-08-04 15:11:35.928
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:11:35.928
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:11:35.939
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:11:35.943
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:11:35.952
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 16.6449 ms

时间:2025-08-04 15:11:35.961
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 32.9537ms

时间:2025-08-04 15:11:35.962
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:11:35.977
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:11:35.997
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 17.6018ms.

时间:2025-08-04 15:11:36.172
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:11:36.187
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:11:36.193
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:11:36.197
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:11:36.205
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:11:36.210
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:11:36.215
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:11:36.224
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:11:36.242
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 15.7854ms.

时间:2025-08-04 15:11:36.488
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 259.5344ms.

时间:2025-08-04 15:11:36.548
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:11:36.555
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 578.5954ms

时间:2025-08-04 15:11:36.557
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:11:36.560
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 621.0015 ms

时间:2025-08-04 15:11:36.563
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 634.7549ms

时间:2025-08-04 15:11:37.506
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:11:37.524
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 1310.0724ms

时间:2025-08-04 15:11:37.563
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:11:37.640
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 1445.0948 ms

时间:2025-08-04 15:11:37.642
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 1455.067ms

时间:2025-08-04 15:11:37.736
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:11:37.739
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 1517.6503ms

时间:2025-08-04 15:11:37.742
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:11:37.745
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 1554.8884 ms

时间:2025-08-04 15:11:37.748
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 1576.0465ms

时间:2025-08-04 15:11:37.769
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:11:37.815
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:11:37.907
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 99.5508 ms

时间:2025-08-04 15:11:37.912
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 142.8512ms

时间:2025-08-04 15:11:42.314
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc&name=%E4%BF%A1" - null null

时间:2025-08-04 15:11:42.322
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:11:42.328
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:11:42.335
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:11:42.351
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 14.3238ms.

时间:2025-08-04 15:11:42.485
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:11:42.492
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 158.0943ms

时间:2025-08-04 15:11:42.494
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:11:42.496
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 175.5290 ms

时间:2025-08-04 15:11:42.500
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc&name=%E4%BF%A1" - 200 47 "application/json; charset=utf-8" 185.9695ms

时间:2025-08-04 15:13:13.338
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:13:13.346
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:13:13.353
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:13.358
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:13.388
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 28.0276ms.

时间:2025-08-04 15:13:13.392
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:13.394
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 38.3611ms

时间:2025-08-04 15:13:13.402
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:13:13.409
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 66.5404 ms

时间:2025-08-04 15:13:13.417
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 79.2743ms

时间:2025-08-04 15:13:18.898
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:13:18.911
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:13:18.917
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:18.920
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:18.929
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 6.6542ms.

时间:2025-08-04 15:13:18.971
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:13:18.977
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:13:18.983
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:18.988
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:19.015
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 24.4571ms.

时间:2025-08-04 15:13:19.019
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:19.022
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 34.5546ms

时间:2025-08-04 15:13:19.025
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:13:19.027
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 50.9050 ms

时间:2025-08-04 15:13:19.030
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 58.5686ms

时间:2025-08-04 15:13:19.113
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:19.115
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 194.7031ms

时间:2025-08-04 15:13:19.117
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:13:19.119
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 212.4139 ms

时间:2025-08-04 15:13:19.122
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 223.7865ms

时间:2025-08-04 15:13:19.295
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:13:19.302
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:13:19.307
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:19.311
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:19.314
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4742ms.

时间:2025-08-04 15:13:19.672
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:19.676
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 365.6752ms

时间:2025-08-04 15:13:19.683
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:13:19.686
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 385.3831 ms

时间:2025-08-04 15:13:19.695
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 400.6183ms

时间:2025-08-04 15:13:21.968
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:13:21.998
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:13:22.002
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:13:22.002
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:22.020
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:22.023
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:22.026
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:13:22.035
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:22.039
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 65.3592 ms

时间:2025-08-04 15:13:22.043
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 74.6253ms

时间:2025-08-04 15:13:22.055
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 14.4708ms.

时间:2025-08-04 15:13:22.056
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:22.062
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:22.183
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 119.1034ms.

时间:2025-08-04 15:13:22.245
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:22.248
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 213.4845ms

时间:2025-08-04 15:13:22.254
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:22.256
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 255.4576 ms

时间:2025-08-04 15:13:22.259
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 260.7533ms

时间:2025-08-04 15:13:22.449
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:13:22.456
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:22.461
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:22.462
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 400.7396ms

时间:2025-08-04 15:13:22.463
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:22.465
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 449.4981 ms

时间:2025-08-04 15:13:22.467
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 465.1604ms

时间:2025-08-04 15:13:22.470
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:22.476
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:22.491
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 13.2115ms.

时间:2025-08-04 15:13:22.531
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:13:22.540
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:13:22.543
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 5.0814 ms

时间:2025-08-04 15:13:22.545
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 14.2438ms

时间:2025-08-04 15:13:22.612
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:22.615
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 139.2075ms

时间:2025-08-04 15:13:22.617
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:22.619
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 165.2719 ms

时间:2025-08-04 15:13:22.622
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 172.966ms

时间:2025-08-04 15:13:23.105
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:13:23.117
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:13:23.119
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 8.0989 ms

时间:2025-08-04 15:13:23.123
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 17.2713ms

时间:2025-08-04 15:13:23.135
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:13:23.136
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:13:23.141
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:23.151
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:23.156
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:23.158
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:23.160
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:23.165
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:23.172
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.7331ms.

时间:2025-08-04 15:13:23.216
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 46.6858ms.

时间:2025-08-04 15:13:23.346
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:23.353
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 193.0174ms

时间:2025-08-04 15:13:23.357
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:23.359
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 219.8607 ms

时间:2025-08-04 15:13:23.362
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 227.0505ms

时间:2025-08-04 15:13:23.513
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:23.516
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 351.3386ms

时间:2025-08-04 15:13:23.520
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:13:23.523
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:23.527
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:23.527
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 377.4253 ms

时间:2025-08-04 15:13:23.531
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 395.2252ms

时间:2025-08-04 15:13:23.532
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:13:23.541
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:13:23.553
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 10.621ms.

时间:2025-08-04 15:13:23.569
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:13:23.575
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:13:23.579
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 5.7628 ms

时间:2025-08-04 15:13:23.582
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 13.4623ms

时间:2025-08-04 15:13:23.721
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:13:23.728
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 187.5933ms

时间:2025-08-04 15:13:23.735
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:13:23.744
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 217.9852 ms

时间:2025-08-04 15:13:23.748
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 227.4561ms

时间:2025-08-04 15:14:00.547
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:14:00.553
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:14:00.560
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:00.566
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:00.574
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:14:00.579
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:14:00.589
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:00.594
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:00.624
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 54.0147ms.

时间:2025-08-04 15:14:00.631
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:00.637
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 71.6913ms

时间:2025-08-04 15:14:00.644
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:14:00.648
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 45.6601ms.

时间:2025-08-04 15:14:00.650
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 97.8936 ms

时间:2025-08-04 15:14:00.653
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:00.656
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 109.0497ms

时间:2025-08-04 15:14:00.660
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 66.759ms

时间:2025-08-04 15:14:00.674
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:14:00.678
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 100.3193 ms

时间:2025-08-04 15:14:00.680
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 106.1922ms

时间:2025-08-04 15:14:00.692
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:14:00.698
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:14:00.704
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:00.711
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:00.714
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.5518ms.

时间:2025-08-04 15:14:00.721
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:14:00.727
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:14:00.732
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:00.737
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:00.742
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.7866ms.

时间:2025-08-04 15:14:00.842
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:00.844
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 133.3137ms

时间:2025-08-04 15:14:00.849
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:14:00.852
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 155.5189 ms

时间:2025-08-04 15:14:00.859
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 166.3131ms

时间:2025-08-04 15:14:01.137
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:01.141
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 403.5318ms

时间:2025-08-04 15:14:01.151
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:14:01.153
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 427.5976 ms

时间:2025-08-04 15:14:01.158
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 437.1654ms

时间:2025-08-04 15:14:06.001
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:14:06.012
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:14:06.018
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 8.1465 ms

时间:2025-08-04 15:14:06.025
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 23.8561ms

时间:2025-08-04 15:14:06.061
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:14:06.064
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:14:06.067
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:06.074
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:06.080
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:06.084
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:06.093
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:06.102
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:06.112
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 8.3123ms.

时间:2025-08-04 15:14:06.183
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 73.9254ms.

时间:2025-08-04 15:14:06.263
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:06.265
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 177.6215ms

时间:2025-08-04 15:14:06.268
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:06.271
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 205.2294 ms

时间:2025-08-04 15:14:06.275
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 214.2101ms

时间:2025-08-04 15:14:06.476
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:06.479
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 377.9648ms

时间:2025-08-04 15:14:06.482
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:06.484
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 412.0674 ms

时间:2025-08-04 15:14:06.487
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 423.9072ms

时间:2025-08-04 15:14:09.309
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:14:09.318
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:14:09.326
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 9.8854 ms

时间:2025-08-04 15:14:09.333
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:14:09.345
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 35.9129ms

时间:2025-08-04 15:14:09.350
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:09.354
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:14:09.364
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:09.370
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:09.377
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:09.388
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.8558ms.

时间:2025-08-04 15:14:09.401
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:09.414
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:09.485
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 69.4308ms.

时间:2025-08-04 15:14:09.513
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:09.515
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 138.7349ms

时间:2025-08-04 15:14:09.517
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:09.519
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 170.3715 ms

时间:2025-08-04 15:14:09.523
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 190.0283ms

时间:2025-08-04 15:14:09.831
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:09.924
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 511.0573ms

时间:2025-08-04 15:14:09.931
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:09.933
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 567.0295 ms

时间:2025-08-04 15:14:09.936
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 581.762ms

时间:2025-08-04 15:14:11.484
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:14:11.493
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:14:11.499
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:11.507
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:11.558
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 48.7686ms.

时间:2025-08-04 15:14:11.581
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:11.584
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 77.4641ms

时间:2025-08-04 15:14:11.590
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:14:11.592
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 100.6697 ms

时间:2025-08-04 15:14:11.599
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 114.3365ms

时间:2025-08-04 15:14:11.923
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:14:11.930
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:14:11.935
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:11.941
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:11.945
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.6133ms.

时间:2025-08-04 15:14:12.080
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:12.099
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 158.2448ms

时间:2025-08-04 15:14:12.103
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:14:12.106
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 177.2517 ms

时间:2025-08-04 15:14:12.111
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 187.923ms

时间:2025-08-04 15:14:13.334
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:14:13.341
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:14:13.345
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 4.6057 ms

时间:2025-08-04 15:14:13.349
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 15.3302ms

时间:2025-08-04 15:14:13.358
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:14:13.365
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:14:13.369
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:13.374
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:13.385
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:13.386
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:13.403
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:13.407
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:13.437
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 18.8847ms.

时间:2025-08-04 15:14:13.469
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 46.9086ms.

时间:2025-08-04 15:14:13.564
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:14:13.573
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:13.580
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:13.586
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:13.604
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 16.2508ms.

时间:2025-08-04 15:14:13.790
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:13.793
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 207.1712ms

时间:2025-08-04 15:14:13.795
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:13.798
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 227.5013 ms

时间:2025-08-04 15:14:13.802
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 237.8114ms

时间:2025-08-04 15:14:13.926
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:13.931
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 528.6263ms

时间:2025-08-04 15:14:13.941
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:13.943
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 575.5774 ms

时间:2025-08-04 15:14:13.952
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 594.3868ms

时间:2025-08-04 15:14:14.172
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:14.438
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 1031.8106ms

时间:2025-08-04 15:14:14.499
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:14.517
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 1143.5680 ms

时间:2025-08-04 15:14:14.519
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 1153.523ms

时间:2025-08-04 15:14:14.627
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:14:14.635
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:14:14.637
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 3.0617 ms

时间:2025-08-04 15:14:14.640
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 13.5005ms

时间:2025-08-04 15:14:15.760
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:14:15.770
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:14:15.780
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:15.794
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:15.843
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 41.1366ms.

时间:2025-08-04 15:14:15.850
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:15.852
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 64.7284ms

时间:2025-08-04 15:14:15.857
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:14:15.859
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 92.9707 ms

时间:2025-08-04 15:14:15.865
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 105.2236ms

时间:2025-08-04 15:14:15.877
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:14:15.894
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:14:15.900
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:15.905
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:15.912
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.8155ms.

时间:2025-08-04 15:14:16.100
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:16.102
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 197.8373ms

时间:2025-08-04 15:14:16.105
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:14:16.108
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 215.4305 ms

时间:2025-08-04 15:14:16.111
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 234.2423ms

时间:2025-08-04 15:14:17.495
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:14:17.503
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:14:17.509
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 6.6587 ms

时间:2025-08-04 15:14:17.513
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 18.2217ms

时间:2025-08-04 15:14:17.526
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:14:17.527
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:14:17.535
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:17.540
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:17.545
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:17.551
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:17.551
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:17.564
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:17.571
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 16.1179ms.

时间:2025-08-04 15:14:17.619
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 46.2747ms.

时间:2025-08-04 15:14:17.727
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:17.729
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 178.3609ms

时间:2025-08-04 15:14:17.731
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:17.733
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 200.1750 ms

时间:2025-08-04 15:14:17.736
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 210.1509ms

时间:2025-08-04 15:14:17.933
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:17.937
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 374.121ms

时间:2025-08-04 15:14:17.944
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:17.945
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 406.6411 ms

时间:2025-08-04 15:14:17.948
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 421.379ms

时间:2025-08-04 15:14:17.980
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:14:17.986
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:14:17.994
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 9.0928 ms

时间:2025-08-04 15:14:17.997
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 16.0282ms

时间:2025-08-04 15:14:18.446
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:14:18.452
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:18.461
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:14:18.468
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:14:18.492
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 17.9701ms.

时间:2025-08-04 15:14:18.608
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:14:18.611
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 143.4306ms

时间:2025-08-04 15:14:18.621
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:14:18.625
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 174.4878 ms

时间:2025-08-04 15:14:18.635
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 188.4065ms

时间:2025-08-04 15:16:10.799
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:16:10.805
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:10.809
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:10.821
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:10.931
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 61.8517ms.

时间:2025-08-04 15:16:10.934
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:10.935
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 115.3993ms

时间:2025-08-04 15:16:10.941
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:10.942
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 138.8991 ms

时间:2025-08-04 15:16:10.947
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 148.3737ms

时间:2025-08-04 15:16:11.079
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:16:11.089
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:11.096
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:11.103
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:11.106
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4883ms.

时间:2025-08-04 15:16:11.240
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:11.242
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 139.064ms

时间:2025-08-04 15:16:11.244
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:11.247
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 159.2990 ms

时间:2025-08-04 15:16:11.250
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 171.438ms

时间:2025-08-04 15:16:11.400
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:16:11.405
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:11.412
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:11.416
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:11.450
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 31.3573ms.

时间:2025-08-04 15:16:11.452
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:11.454
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 38.1574ms

时间:2025-08-04 15:16:11.459
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:11.465
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 61.2294 ms

时间:2025-08-04 15:16:11.468
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 68.032ms

时间:2025-08-04 15:16:11.788
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:16:11.794
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:11.799
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:11.803
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:11.806
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4612ms.

时间:2025-08-04 15:16:11.930
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:11.932
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 129.2779ms

时间:2025-08-04 15:16:11.937
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:11.939
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 146.6634 ms

时间:2025-08-04 15:16:11.945
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 156.4516ms

时间:2025-08-04 15:16:13.674
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:16:13.681
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:16:13.684
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 3.9906 ms

时间:2025-08-04 15:16:13.688
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 14.3125ms

时间:2025-08-04 15:16:13.706
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:16:13.707
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:16:13.713
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:13.717
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:13.723
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:13.724
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:13.729
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:13.736
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:13.751
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 11.0775ms.

时间:2025-08-04 15:16:13.803
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 56.5398ms.

时间:2025-08-04 15:16:13.966
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:13.968
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 239.021ms

时间:2025-08-04 15:16:13.971
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:13.975
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 263.6895 ms

时间:2025-08-04 15:16:13.979
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 272.9985ms

时间:2025-08-04 15:16:14.079
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:14.084
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 348.4764ms

时间:2025-08-04 15:16:14.092
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:14.097
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 381.3465 ms

时间:2025-08-04 15:16:14.108
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 400.2123ms

时间:2025-08-04 15:16:14.166
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:16:14.177
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:16:14.181
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 7.5135 ms

时间:2025-08-04 15:16:14.186
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 19.2186ms

时间:2025-08-04 15:16:14.194
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:16:14.202
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:16:14.217
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 16.3470 ms

时间:2025-08-04 15:16:14.220
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 26.2297ms

时间:2025-08-04 15:16:14.321
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:16:14.328
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:16:14.385
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:14.386
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:14.392
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:14.392
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:14.412
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:14.498
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:14.518
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 6.1996ms.

时间:2025-08-04 15:16:14.819
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:14.855
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 354.4234ms.

时间:2025-08-04 15:16:14.855
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 358.1815ms

时间:2025-08-04 15:16:14.888
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:14.890
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 507.3919 ms

时间:2025-08-04 15:16:14.892
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 571.7039ms

时间:2025-08-04 15:16:14.993
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:15.353
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 945.2971ms

时间:2025-08-04 15:16:15.385
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:15.388
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 1002.5999 ms

时间:2025-08-04 15:16:15.392
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 1064.6599ms

时间:2025-08-04 15:16:16.776
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:16:16.784
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:16.793
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:16:16.798
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:16.818
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:16.822
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:16.854
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 33.724ms.

时间:2025-08-04 15:16:16.867
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:16.886
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:16.907
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 17.7569ms.

时间:2025-08-04 15:16:16.945
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:16:16.954
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:16:16.976
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 23.1974 ms

时间:2025-08-04 15:16:16.987
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 41.8246ms

时间:2025-08-04 15:16:16.990
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:17.000
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 181.98ms

时间:2025-08-04 15:16:17.001
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:17.003
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 221.0567 ms

时间:2025-08-04 15:16:17.006
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 229.5089ms

时间:2025-08-04 15:16:17.275
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:17.284
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 398.4991ms

时间:2025-08-04 15:16:17.302
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:17.307
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 492.1571 ms

时间:2025-08-04 15:16:17.325
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 532.5908ms

时间:2025-08-04 15:16:29.509
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:16:29.511
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:16:29.516
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:29.520
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:29.525
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:29.531
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:29.531
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:29.537
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:29.596
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 57.4151ms.

时间:2025-08-04 15:16:29.600
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 57.2557ms.

时间:2025-08-04 15:16:29.605
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:29.608
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:29.610
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 79.0277ms

时间:2025-08-04 15:16:29.613
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 77.5319ms

时间:2025-08-04 15:16:29.616
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:29.619
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:29.621
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 106.4792 ms

时间:2025-08-04 15:16:29.624
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 104.7328 ms

时间:2025-08-04 15:16:29.629
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 119.3468ms

时间:2025-08-04 15:16:29.632
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 121.1439ms

时间:2025-08-04 15:16:30.139
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:16:30.146
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:16:30.149
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:30.154
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:30.157
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:30.158
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:30.165
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:30.170
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:30.173
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4237ms.

时间:2025-08-04 15:16:30.175
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4056ms.

时间:2025-08-04 15:16:30.297
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:30.299
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 134.3357ms

时间:2025-08-04 15:16:30.302
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:30.306
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 157.5918 ms

时间:2025-08-04 15:16:30.313
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 173.9124ms

时间:2025-08-04 15:16:30.492
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:30.496
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 326.0218ms

时间:2025-08-04 15:16:30.500
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:30.501
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 349.3535 ms

时间:2025-08-04 15:16:30.507
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 361.238ms

时间:2025-08-04 15:16:32.347
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:16:32.353
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:16:32.356
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 3.6091 ms

时间:2025-08-04 15:16:32.359
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 12.1851ms

时间:2025-08-04 15:16:32.389
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:16:32.390
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:16:32.397
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:32.401
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:32.405
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:32.406
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:32.410
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:32.419
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:32.432
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 10.9621ms.

时间:2025-08-04 15:16:32.479
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 55.5082ms.

时间:2025-08-04 15:16:32.517
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:16:32.524
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:32.534
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:32.543
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:32.553
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:32.556
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 146.6641ms

时间:2025-08-04 15:16:32.558
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:32.562
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 16.6665ms.

时间:2025-08-04 15:16:32.564
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 170.9213 ms

时间:2025-08-04 15:16:32.572
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 183.34ms

时间:2025-08-04 15:16:32.684
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:32.686
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 143.8721ms

时间:2025-08-04 15:16:32.688
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:32.691
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 168.4875 ms

时间:2025-08-04 15:16:32.699
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 182.0314ms

时间:2025-08-04 15:16:32.840
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:32.852
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 433.8162ms

时间:2025-08-04 15:16:32.868
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:32.870
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 469.2887 ms

时间:2025-08-04 15:16:32.872
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 481.9285ms

时间:2025-08-04 15:16:32.920
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:16:32.931
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:16:32.937
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 11.8807 ms

时间:2025-08-04 15:16:32.945
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 22.6578ms

时间:2025-08-04 15:16:33.098
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:16:33.108
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:16:33.117
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 10.9678 ms

时间:2025-08-04 15:16:33.120
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 22.4374ms

时间:2025-08-04 15:16:33.136
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:16:33.138
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:16:33.154
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:33.167
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:33.167
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:33.172
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:33.182
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:33.203
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:33.213
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 32.7303ms.

时间:2025-08-04 15:16:33.264
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 51.9713ms.

时间:2025-08-04 15:16:33.318
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:33.319
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 147.6851ms

时间:2025-08-04 15:16:33.322
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:33.324
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 170.6698 ms

时间:2025-08-04 15:16:33.326
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 190.0049ms

时间:2025-08-04 15:16:33.448
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:16:33.454
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:33.464
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:33.472
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:33.492
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 17.6497ms.

时间:2025-08-04 15:16:33.743
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:33.748
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 545.9649ms

时间:2025-08-04 15:16:33.751
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:33.753
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 587.2360 ms

时间:2025-08-04 15:16:33.758
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 620.5799ms

时间:2025-08-04 15:16:33.796
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:16:33.802
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:16:33.807
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 5.9234 ms

时间:2025-08-04 15:16:33.812
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 15.9565ms

时间:2025-08-04 15:16:33.967
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:33.971
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 499.6723ms

时间:2025-08-04 15:16:34.006
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:16:34.009
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 556.9531 ms

时间:2025-08-04 15:16:34.038
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 589.7301ms

时间:2025-08-04 15:16:58.255
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:16:58.261
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:58.267
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:58.271
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:58.298
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:16:58.303
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:58.309
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:58.309
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 36.0897ms.

时间:2025-08-04 15:16:58.315
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:58.319
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:58.323
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 52.3755ms

时间:2025-08-04 15:16:58.325
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:58.339
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 79.4356 ms

时间:2025-08-04 15:16:58.348
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 92.5399ms

时间:2025-08-04 15:16:58.371
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 49.7351ms.

时间:2025-08-04 15:16:58.374
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:58.377
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 62.3801ms

时间:2025-08-04 15:16:58.382
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:16:58.384
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 81.9206 ms

时间:2025-08-04 15:16:58.387
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 89.042ms

时间:2025-08-04 15:16:58.596
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:16:58.598
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:16:58.603
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:58.609
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:58.620
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:58.623
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:16:58.624
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:58.632
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:16:58.635
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.47ms.

时间:2025-08-04 15:16:58.638
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4367ms.

时间:2025-08-04 15:16:58.766
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:58.769
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 144.5035ms

时间:2025-08-04 15:16:58.774
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:58.776
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 173.8506 ms

时间:2025-08-04 15:16:58.780
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 183.6591ms

时间:2025-08-04 15:16:58.943
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:16:58.947
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 314.8096ms

时间:2025-08-04 15:16:58.949
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:16:58.953
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 347.1705 ms

时间:2025-08-04 15:16:58.958
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 360.2513ms

时间:2025-08-04 15:17:01.696
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:01.697
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:17:01.697
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:17:01.702
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:01.711
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:01.719
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:01.723
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:01.731
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:01.732
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 24.1490 ms

时间:2025-08-04 15:17:01.737
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:01.738
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 11.6389 ms

时间:2025-08-04 15:17:01.738
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:01.743
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 46.5037ms

时间:2025-08-04 15:17:01.750
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:01.752
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 50.1198ms

时间:2025-08-04 15:17:01.753
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:17:01.767
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:17:01.781
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:01.787
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:01.793
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:01.797
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 25.9028ms.

时间:2025-08-04 15:17:01.806
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:01.807
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:01.820
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:01.827
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:01.848
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 15.7753ms.

时间:2025-08-04 15:17:01.884
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 83.3371ms.

时间:2025-08-04 15:17:01.899
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 63.0745ms.

时间:2025-08-04 15:17:01.907
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:01.910
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 160.3043ms

时间:2025-08-04 15:17:01.923
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:01.931
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 214.0996 ms

时间:2025-08-04 15:17:01.935
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 237.8099ms

时间:2025-08-04 15:17:02.059
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:02.061
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 279.512ms

时间:2025-08-04 15:17:02.070
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:02.076
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 353.6554 ms

时间:2025-08-04 15:17:02.080
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 382.7654ms

时间:2025-08-04 15:17:02.118
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:02.127
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:02.141
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 14.7083 ms

时间:2025-08-04 15:17:02.200
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 82.1088ms

时间:2025-08-04 15:17:02.241
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:02.243
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 423.391ms

时间:2025-08-04 15:17:02.250
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:02.253
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 467.1075 ms

时间:2025-08-04 15:17:02.257
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 503.8998ms

时间:2025-08-04 15:17:02.394
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:02.453
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 626.5154ms

时间:2025-08-04 15:17:02.467
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:02.469
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 677.1343 ms

时间:2025-08-04 15:17:02.474
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 706.8576ms

时间:2025-08-04 15:17:02.647
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:17:02.653
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:02.658
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:02.670
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:02.686
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 13.254ms.

时间:2025-08-04 15:17:02.721
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:17:02.725
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:02.727
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:02.732
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:02.736
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 5.1594 ms

时间:2025-08-04 15:17:02.737
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:02.741
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 15.3435ms

时间:2025-08-04 15:17:02.746
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:02.767
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 12.8525ms.

时间:2025-08-04 15:17:02.799
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:02.804
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 134.7653ms

时间:2025-08-04 15:17:02.806
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:02.809
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 157.4478 ms

时间:2025-08-04 15:17:02.813
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 165.9353ms

时间:2025-08-04 15:17:02.907
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:02.910
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 164.0652ms

时间:2025-08-04 15:17:02.915
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:02.916
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 190.5802 ms

时间:2025-08-04 15:17:02.919
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 197.7273ms

时间:2025-08-04 15:17:16.572
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:17:16.578
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:16.586
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:16.590
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:16.632
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 39.4611ms.

时间:2025-08-04 15:17:16.635
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:16.636
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 46.4925ms

时间:2025-08-04 15:17:16.639
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:16.641
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 64.2898 ms

时间:2025-08-04 15:17:16.644
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 71.5957ms

时间:2025-08-04 15:17:16.710
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:17:16.717
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:16.723
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:16.733
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:16.738
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.5994ms.

时间:2025-08-04 15:17:17.038
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:17:17.044
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:17.052
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:17.058
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:17.093
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 32.1569ms.

时间:2025-08-04 15:17:17.100
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:17.101
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 44.3646ms

时间:2025-08-04 15:17:17.106
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:17.108
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 65.6513 ms

时间:2025-08-04 15:17:17.118
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 79.8005ms

时间:2025-08-04 15:17:17.163
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:17.167
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 434.0579ms

时间:2025-08-04 15:17:17.169
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:17.174
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 457.5844 ms

时间:2025-08-04 15:17:17.177
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 466.6888ms

时间:2025-08-04 15:17:17.287
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:17:17.293
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:17.302
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:17.306
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:17.309
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4859ms.

时间:2025-08-04 15:17:17.546
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:17.552
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 246.4568ms

时间:2025-08-04 15:17:17.554
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:17.556
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 263.4385 ms

时间:2025-08-04 15:17:17.558
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 271.2937ms

时间:2025-08-04 15:17:19.309
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:19.312
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:17:19.314
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:17:19.321
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:19.323
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:19.327
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:19.328
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 8.9111 ms

时间:2025-08-04 15:17:19.336
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:19.338
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 27.9874ms

时间:2025-08-04 15:17:19.338
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:19.342
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:19.359
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:19.373
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 6.9725ms.

时间:2025-08-04 15:17:19.434
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 64.1357ms.

时间:2025-08-04 15:17:19.503
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:19.506
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 164.5534ms

时间:2025-08-04 15:17:19.509
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:19.511
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 188.9331 ms

时间:2025-08-04 15:17:19.515
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 203.1756ms

时间:2025-08-04 15:17:19.539
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:17:19.544
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:19.551
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:19.556
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:19.571
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 12.9248ms.

时间:2025-08-04 15:17:19.705
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:19.708
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 152.2881ms

时间:2025-08-04 15:17:19.713
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:19.715
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 172.2043 ms

时间:2025-08-04 15:17:19.720
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 178.806ms

时间:2025-08-04 15:17:19.793
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:19.800
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:19.806
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 6.7629 ms

时间:2025-08-04 15:17:19.810
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 16.513ms

时间:2025-08-04 15:17:19.901
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:19.907
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 547.9707ms

时间:2025-08-04 15:17:19.924
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:19.927
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 601.4254 ms

时间:2025-08-04 15:17:19.932
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 618.0397ms

时间:2025-08-04 15:17:20.008
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:20.016
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:20.023
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 8.7307 ms

时间:2025-08-04 15:17:20.025
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 17.6236ms

时间:2025-08-04 15:17:20.102
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:17:20.108
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:20.122
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:17:20.126
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:20.136
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:20.139
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:20.158
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:20.165
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:20.181
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 10.7246ms.

时间:2025-08-04 15:17:20.211
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 66.1605ms.

时间:2025-08-04 15:17:20.371
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:20.374
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 208.8834ms

时间:2025-08-04 15:17:20.382
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:20.385
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 250.2509 ms

时间:2025-08-04 15:17:20.390
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 267.6913ms

时间:2025-08-04 15:17:20.450
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:17:20.457
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:20.468
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:20.476
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:20.478
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:20.483
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 345.0158ms

时间:2025-08-04 15:17:20.485
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:20.487
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 379.8985 ms

时间:2025-08-04 15:17:20.494
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 392.3307ms

时间:2025-08-04 15:17:20.501
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 18.5623ms.

时间:2025-08-04 15:17:20.565
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:20.572
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:20.584
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 13.6337 ms

时间:2025-08-04 15:17:20.590
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 24.8253ms

时间:2025-08-04 15:17:20.620
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:20.622
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 146.3165ms

时间:2025-08-04 15:17:20.628
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:20.634
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 180.1719 ms

时间:2025-08-04 15:17:20.637
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 187.5956ms

时间:2025-08-04 15:17:37.651
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:17:37.656
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:37.661
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:37.667
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:37.700
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 30.6066ms.

时间:2025-08-04 15:17:37.706
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:37.708
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 40.7693ms

时间:2025-08-04 15:17:37.711
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:37.714
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 58.2793 ms

时间:2025-08-04 15:17:37.717
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 65.5747ms

时间:2025-08-04 15:17:37.848
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:17:37.855
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:37.860
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:37.865
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:37.868
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4741ms.

时间:2025-08-04 15:17:37.973
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:17:37.978
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:37.983
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:37.990
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:38.023
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 30.6092ms.

时间:2025-08-04 15:17:38.026
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:38.027
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 37.6841ms

时间:2025-08-04 15:17:38.033
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:38.035
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 58.4543 ms

时间:2025-08-04 15:17:38.038
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 65.6597ms

时间:2025-08-04 15:17:38.083
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:38.086
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 220.6278ms

时间:2025-08-04 15:17:38.088
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:38.091
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 237.1585 ms

时间:2025-08-04 15:17:38.093
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 245.5265ms

时间:2025-08-04 15:17:38.156
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:17:38.162
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:38.167
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:38.182
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:38.186
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.6535ms.

时间:2025-08-04 15:17:38.325
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:38.327
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 145.71ms

时间:2025-08-04 15:17:38.329
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:38.331
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 170.1380 ms

时间:2025-08-04 15:17:38.336
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 179.8494ms

时间:2025-08-04 15:17:40.399
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:40.406
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:40.412
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 7.4074 ms

时间:2025-08-04 15:17:40.415
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 15.5245ms

时间:2025-08-04 15:17:40.454
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:17:40.461
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:40.461
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:17:40.466
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:40.468
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:40.473
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:40.481
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:40.486
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:40.491
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 12.2428ms.

时间:2025-08-04 15:17:40.540
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 47.4642ms.

时间:2025-08-04 15:17:40.840
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:40.842
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 369.4323ms

时间:2025-08-04 15:17:40.844
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:40.848
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 388.5464 ms

时间:2025-08-04 15:17:40.852
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 398.0854ms

时间:2025-08-04 15:17:40.860
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:17:40.870
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:40.875
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:40.885
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:17:40.890
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:40.897
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:17:40.904
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:41.034
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:41.036
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 551.2713ms

时间:2025-08-04 15:17:41.038
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:41.040
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 573.1074 ms

时间:2025-08-04 15:17:41.043
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 583.3084ms

时间:2025-08-04 15:17:41.052
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:41.056
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 172.9812 ms

时间:2025-08-04 15:17:41.060
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 184.9233ms

时间:2025-08-04 15:17:41.071
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:41.073
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:41.074
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:41.075
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:41.078
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:41.090
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:41.106
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 20.8364ms.

时间:2025-08-04 15:17:41.111
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 11.3152ms.

时间:2025-08-04 15:17:41.157
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 73.8574ms.

时间:2025-08-04 15:17:41.203
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:41.214
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:41.217
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 5.1067 ms

时间:2025-08-04 15:17:41.225
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 21.9458ms

时间:2025-08-04 15:17:41.552
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:41.571
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 496.503ms

时间:2025-08-04 15:17:41.583
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:41.590
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 687.0116 ms

时间:2025-08-04 15:17:41.595
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 697.5614ms

时间:2025-08-04 15:17:41.712
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:41.726
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 647.1875ms

时间:2025-08-04 15:17:41.730
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:41.736
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 847.2184 ms

时间:2025-08-04 15:17:41.740
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 855.0607ms

时间:2025-08-04 15:17:41.787
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:41.794
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:41.800
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 8.3186 ms

时间:2025-08-04 15:17:41.804
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 16.8647ms

时间:2025-08-04 15:17:41.841
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:41.842
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:17:41.844
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 754.5096ms

时间:2025-08-04 15:17:41.852
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:41.855
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:41.858
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 988.9662 ms

时间:2025-08-04 15:17:41.860
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:41.860
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 1000.1075ms

时间:2025-08-04 15:17:41.865
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:41.884
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 10.0714ms.

时间:2025-08-04 15:17:41.999
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:42.001
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 136.8279ms

时间:2025-08-04 15:17:42.014
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:42.020
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 169.0209 ms

时间:2025-08-04 15:17:42.024
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 181.1394ms

时间:2025-08-04 15:17:54.320
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:17:54.326
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:54.332
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:54.337
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:54.369
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 30.104ms.

时间:2025-08-04 15:17:54.372
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:54.374
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 37.6339ms

时间:2025-08-04 15:17:54.381
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:54.386
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 60.7400 ms

时间:2025-08-04 15:17:54.388
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 68.0662ms

时间:2025-08-04 15:17:54.428
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:17:54.434
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:54.438
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:54.442
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:54.479
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 34.126ms.

时间:2025-08-04 15:17:54.483
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:54.486
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 44.4976ms

时间:2025-08-04 15:17:54.488
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:17:54.492
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 59.0141 ms

时间:2025-08-04 15:17:54.494
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 65.483ms

时间:2025-08-04 15:17:54.542
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:17:54.548
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:54.554
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:54.558
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:54.561
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.6117ms.

时间:2025-08-04 15:17:54.759
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:17:54.767
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:54.774
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:54.778
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:54.783
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4647ms.

时间:2025-08-04 15:17:54.810
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:54.811
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 253.1424ms

时间:2025-08-04 15:17:54.824
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:54.831
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 284.6961 ms

时间:2025-08-04 15:17:54.838
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 296.04ms

时间:2025-08-04 15:17:55.029
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:55.030
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 252.6525ms

时间:2025-08-04 15:17:55.033
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:17:55.037
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 271.6429 ms

时间:2025-08-04 15:17:55.039
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 280.4977ms

时间:2025-08-04 15:17:56.751
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:56.752
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:17:56.752
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:17:56.760
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:56.762
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:56.767
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:56.768
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 10.8569 ms

时间:2025-08-04 15:17:56.775
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 23.5946ms

时间:2025-08-04 15:17:56.775
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:56.778
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:56.785
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:56.790
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:56.799
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 7.6772ms.

时间:2025-08-04 15:17:56.842
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 47.732ms.

时间:2025-08-04 15:17:56.942
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:56.945
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 159.7381ms

时间:2025-08-04 15:17:56.951
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:56.954
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 193.8255 ms

时间:2025-08-04 15:17:56.957
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 205.1287ms

时间:2025-08-04 15:17:57.119
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:57.121
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 331.9102ms

时间:2025-08-04 15:17:57.128
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:57.133
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 367.1710 ms

时间:2025-08-04 15:17:57.137
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 384.4666ms

时间:2025-08-04 15:17:57.192
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:57.198
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:57.204
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 7.1470 ms

时间:2025-08-04 15:17:57.206
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 14.457ms

时间:2025-08-04 15:17:57.429
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:17:57.435
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:57.440
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:57.459
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:57.478
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 16.4893ms.

时间:2025-08-04 15:17:57.627
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:57.631
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 172.5723ms

时间:2025-08-04 15:17:57.635
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:57.638
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 203.3357 ms

时间:2025-08-04 15:17:57.640
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 210.6136ms

时间:2025-08-04 15:17:57.908
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:57.917
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:57.924
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 8.2684 ms

时间:2025-08-04 15:17:57.926
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 18.0301ms

时间:2025-08-04 15:17:57.948
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:17:57.949
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:17:57.953
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:57.959
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:57.965
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:57.967
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:57.971
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:57.977
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:57.993
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 9.9489ms.

时间:2025-08-04 15:17:58.036
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 56.9896ms.

时间:2025-08-04 15:17:58.119
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:58.121
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 150.6994ms

时间:2025-08-04 15:17:58.126
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:58.129
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 177.4673 ms

时间:2025-08-04 15:17:58.133
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 184.6556ms

时间:2025-08-04 15:17:58.319
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:58.321
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 344.6323ms

时间:2025-08-04 15:17:58.323
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:58.326
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 369.9010 ms

时间:2025-08-04 15:17:58.330
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 380.7665ms

时间:2025-08-04 15:17:58.360
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:17:58.371
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:17:58.377
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 7.4682 ms

时间:2025-08-04 15:17:58.380
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 19.6233ms

时间:2025-08-04 15:17:58.445
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:17:58.451
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:58.456
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:17:58.463
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:17:58.478
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 12.6119ms.

时间:2025-08-04 15:17:58.754
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:17:58.756
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 293.6452ms

时间:2025-08-04 15:17:58.759
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:17:58.763
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 313.2377 ms

时间:2025-08-04 15:17:58.769
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 323.7385ms

时间:2025-08-04 15:18:10.852
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:18:10.853
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:18:10.858
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:18:10.863
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:18:10.868
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:10.870
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:10.872
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:10.876
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:10.918
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 37.3348ms.

时间:2025-08-04 15:18:10.920
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 36.4798ms.

时间:2025-08-04 15:18:10.922
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:10.928
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 55.9983ms

时间:2025-08-04 15:18:10.932
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:10.934
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:18:10.936
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 60.4316ms

时间:2025-08-04 15:18:10.937
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:18:10.942
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 84.1770 ms

时间:2025-08-04 15:18:10.944
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 82.4767 ms

时间:2025-08-04 15:18:10.948
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 96.0474ms

时间:2025-08-04 15:18:10.951
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 98.2492ms

时间:2025-08-04 15:18:10.989
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:18:10.994
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:18:10.999
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:11.004
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:11.007
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.449ms.

时间:2025-08-04 15:18:11.050
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:18:11.057
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:18:11.062
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:11.066
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:11.069
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4749ms.

时间:2025-08-04 15:18:11.122
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:11.124
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 120.0069ms

时间:2025-08-04 15:18:11.128
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:18:11.131
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 138.2341 ms

时间:2025-08-04 15:18:11.136
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 146.6899ms

时间:2025-08-04 15:18:11.364
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:11.366
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 299.9048ms

时间:2025-08-04 15:18:11.369
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:18:11.371
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 316.8287 ms

时间:2025-08-04 15:18:11.374
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 323.9659ms

时间:2025-08-04 15:18:13.039
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:18:13.044
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:18:13.048
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 4.6270 ms

时间:2025-08-04 15:18:13.051
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 11.8787ms

时间:2025-08-04 15:18:13.071
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:18:13.076
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.077
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:18:13.084
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:13.089
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.093
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:13.099
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:13.103
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:13.106
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 7.551ms.

时间:2025-08-04 15:18:13.163
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 52.5933ms.

时间:2025-08-04 15:18:13.206
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:13.210
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 116.9751ms

时间:2025-08-04 15:18:13.214
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.216
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 140.9398 ms

时间:2025-08-04 15:18:13.219
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 148.5294ms

时间:2025-08-04 15:18:13.448
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:13.450
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 347.2749ms

时间:2025-08-04 15:18:13.482
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:18:13.508
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.514
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.516
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 428.6794 ms

时间:2025-08-04 15:18:13.521
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:13.522
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 445.0249ms

时间:2025-08-04 15:18:13.535
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:13.557
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 8.7446ms.

时间:2025-08-04 15:18:13.606
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:18:13.614
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:18:13.622
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 10.6946 ms

时间:2025-08-04 15:18:13.626
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 19.417ms

时间:2025-08-04 15:18:13.660
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:18:13.667
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:18:13.671
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 4.8363 ms

时间:2025-08-04 15:18:13.673
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 12.9184ms

时间:2025-08-04 15:18:13.699
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:13.700
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:18:13.701
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 166.1979ms

时间:2025-08-04 15:18:13.706
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.706
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.710
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 197.6329 ms

时间:2025-08-04 15:18:13.712
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 230.1015ms

时间:2025-08-04 15:18:13.717
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:13.722
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:13.737
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 10.8558ms.

时间:2025-08-04 15:18:13.752
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:18:13.760
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.766
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:13.783
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:13.832
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:13.835
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 44.6481ms.

时间:2025-08-04 15:18:13.836
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 113.777ms

时间:2025-08-04 15:18:13.844
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.847
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 143.0647 ms

时间:2025-08-04 15:18:13.850
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 150.5964ms

时间:2025-08-04 15:18:13.948
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:13.951
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 168.6763ms

时间:2025-08-04 15:18:13.954
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:13.956
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 197.8063 ms

时间:2025-08-04 15:18:13.960
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 208.5884ms

时间:2025-08-04 15:18:14.036
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:18:14.041
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:18:14.044
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 4.1257 ms

时间:2025-08-04 15:18:14.047
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 11.15ms

时间:2025-08-04 15:18:14.430
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:18:14.436
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:14.441
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:14.452
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:14.468
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 12.8262ms.

时间:2025-08-04 15:18:14.610
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:14.613
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 160.837ms

时间:2025-08-04 15:18:14.625
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:14.628
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 192.4174 ms

时间:2025-08-04 15:18:14.633
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 202.3703ms

时间:2025-08-04 15:18:27.064
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:18:27.070
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:18:27.076
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:27.080
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:27.116
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 32.6753ms.

时间:2025-08-04 15:18:27.119
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:27.121
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 41.7819ms

时间:2025-08-04 15:18:27.126
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:18:27.129
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 59.9448 ms

时间:2025-08-04 15:18:27.134
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 70.2016ms

时间:2025-08-04 15:18:27.190
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:18:27.196
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:18:27.200
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:27.207
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:27.237
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 27.0882ms.

时间:2025-08-04 15:18:27.241
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:27.242
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 36.6805ms

时间:2025-08-04 15:18:27.244
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:18:27.246
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 51.6201 ms

时间:2025-08-04 15:18:27.253
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 62.3538ms

时间:2025-08-04 15:18:27.668
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:18:27.736
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:18:27.776
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:18:27.779
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:18:27.784
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:27.786
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:27.788
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:27.791
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:27.794
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.5952ms.

时间:2025-08-04 15:18:27.799
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4455ms.

时间:2025-08-04 15:18:28.126
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:28.143
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 355.1417ms

时间:2025-08-04 15:18:28.158
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:18:28.161
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 385.5649 ms

时间:2025-08-04 15:18:28.164
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 495.5887ms

时间:2025-08-04 15:18:28.294
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:28.297
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 506.2108ms

时间:2025-08-04 15:18:28.301
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:18:28.304
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 526.5453 ms

时间:2025-08-04 15:18:28.312
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 576.0763ms

时间:2025-08-04 15:18:29.814
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:18:29.817
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:18:29.819
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:18:29.822
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:18:29.826
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:29.832
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:29.835
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 14.0636 ms

时间:2025-08-04 15:18:29.842
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 28.8459ms

时间:2025-08-04 15:18:29.845
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:29.850
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:29.855
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:29.862
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:29.873
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 7.3417ms.

时间:2025-08-04 15:18:29.914
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 43.6171ms.

时间:2025-08-04 15:18:29.951
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:18:29.958
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:18:29.961
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 4.6376 ms

时间:2025-08-04 15:18:29.964
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 12.6237ms

时间:2025-08-04 15:18:30.001
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:30.004
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 148.3915ms

时间:2025-08-04 15:18:30.009
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:30.011
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 186.1474 ms

时间:2025-08-04 15:18:30.017
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 199.4292ms

时间:2025-08-04 15:18:30.212
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:30.216
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 352.836ms

时间:2025-08-04 15:18:30.219
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:30.222
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 390.8015 ms

时间:2025-08-04 15:18:30.226
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 407.4003ms

时间:2025-08-04 15:18:30.253
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - null null

时间:2025-08-04 15:18:30.258
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:30.261
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:18:30.263
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:30.265
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - null null

时间:2025-08-04 15:18:30.270
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:18:30.272
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:30.278
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:30.283
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 14.6214 ms

时间:2025-08-04 15:18:30.295
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 34.0686ms

时间:2025-08-04 15:18:30.298
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 12.1201ms.

时间:2025-08-04 15:18:30.306
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.UserManagement\", action = \"Query\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.UserQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.UserController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:30.320
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:30.453
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 130.5298ms.

时间:2025-08-04 15:18:30.474
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:18:30.476
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:30.479
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:30.479
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 207.0607ms

时间:2025-08-04 15:18:30.487
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:30.492
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 234.9894 ms

时间:2025-08-04 15:18:30.493
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:30.496
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=user_status" - 200 449 "application/json; charset=utf-8" 242.9039ms

时间:2025-08-04 15:18:30.509
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:30.537
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 13.564ms.

时间:2025-08-04 15:18:30.593
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:30.595
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)" in 275.6105ms

时间:2025-08-04 15:18:30.603
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.UserController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:30.605
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/user/query" responded 200 in 328.5561 ms

时间:2025-08-04 15:18:30.609
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/user/query""?pageIndex=1&pageSize=10&sortFields=id%20desc" - 200 1796 "application/json; charset=utf-8" 344.2823ms

时间:2025-08-04 15:18:30.639
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:18:30.649
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:18:30.654
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 8.2606 ms

时间:2025-08-04 15:18:30.657
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 17.5749ms

时间:2025-08-04 15:18:30.919
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - null null

时间:2025-08-04 15:18:30.946
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:30.955
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:18:30.966
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:18:30.985
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 12.7366ms.

时间:2025-08-04 15:18:31.045
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:31.046
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 537.2362ms

时间:2025-08-04 15:18:31.050
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:31.052
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 574.5790 ms

时间:2025-08-04 15:18:31.056
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 582.3759ms

时间:2025-08-04 15:18:31.268
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:18:31.271
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 306.4181ms

时间:2025-08-04 15:18:31.274
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:18:31.276
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 330.4406 ms

时间:2025-08-04 15:18:31.279
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?SortFields=sort+asc" - 200 304 "application/json; charset=utf-8" 360.1065ms

时间:2025-08-04 15:22:35.867
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:22:35.875
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:22:35.882
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:22:35.886
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:22:35.921
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 31.838ms.

时间:2025-08-04 15:22:35.923
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:22:35.926
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 40.4705ms

时间:2025-08-04 15:22:35.931
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:22:35.933
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 59.7342 ms

时间:2025-08-04 15:22:35.937
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 69.5151ms

时间:2025-08-04 15:22:35.943
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:22:35.950
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:22:35.954
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:22:35.961
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:22:35.963
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.4166ms.

时间:2025-08-04 15:22:36.115
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:22:36.119
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 157.7365ms

时间:2025-08-04 15:22:36.123
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:22:36.125
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 176.7860 ms

时间:2025-08-04 15:22:36.128
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 184.0492ms

时间:2025-08-04 15:22:37.105
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:22:37.113
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:The file "/uploads/file/avatar/20231010143458_1711631391147429888.png" was not modified

时间:2025-08-04 15:22:37.115
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 304 in 3.0853 ms

时间:2025-08-04 15:22:37.120
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 304 null "image/png" 15.3974ms

时间:2025-08-04 16:43:06.300
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 16:43:06.436
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 16:43:06.469
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 16:43:06.475
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 16:43:06.566
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 89.0573ms.

时间:2025-08-04 16:43:06.569
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 16:43:06.571
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 98.6186ms

时间:2025-08-04 16:43:06.576
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 16:43:06.589
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 16:43:06.590
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 285.0192 ms

时间:2025-08-04 16:43:06.594
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 16:43:06.594
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 293.5972ms

时间:2025-08-04 16:43:06.606
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 16:43:06.611
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 16:43:06.616
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 0.5613ms.

时间:2025-08-04 16:43:06.901
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 16:43:06.909
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 298.9042ms

时间:2025-08-04 16:43:06.912
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 16:43:06.915
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 322.3835 ms

时间:2025-08-04 16:43:06.917
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 327.7977ms

时间:2025-08-04 16:43:07.957
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 16:43:08.444
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:The file "/uploads/file/avatar/20231010143458_1711631391147429888.png" was not modified

时间:2025-08-04 16:43:08.456
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 304 in 13.9758 ms

时间:2025-08-04 16:43:08.495
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 304 null "image/png" 537.454ms
