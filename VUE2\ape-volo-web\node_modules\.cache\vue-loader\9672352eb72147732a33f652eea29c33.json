{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\system\\tasks\\index.vue?vue&type=template&id=9a1f6b6a", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\system\\tasks\\index.vue", "mtime": 1754292873038}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754288975174}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}], "contextDependencies": [], "result": ["\n<div class=\"app-container\">\n  <!--工具栏-->\n  <div class=\"head-container\">\n    <div v-if=\"crud.props.searchToggle\">\n      <!-- 搜索 -->\n      <el-input\n        v-model=\"query.taskName\"\n        clearable\n        size=\"small\"\n        placeholder=\"输入任务名称搜索\"\n        style=\"width: 200px\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"toQuery\"\n      />\n      <date-range-picker v-model=\"query.createTime\" class=\"date-item\" />\n      <rrOperation />\n    </div>\n    <crudOperation :permission=\"permission\" />\n    <Log ref=\"log\" />\n  </div>\n  <!--Form表单-->\n  <el-dialog\n    :close-on-click-modal=\"false\"\n    :before-close=\"crud.cancelCU\"\n    :visible.sync=\"crud.status.cu > 0\"\n    :title=\"crud.status.title\"\n    append-to-body\n    width=\"730px\"\n  >\n    <el-form\n      ref=\"form\"\n      :inline=\"true\"\n      :model=\"form\"\n      :rules=\"rules\"\n      size=\"small\"\n      label-width=\"100px\"\n    >\n      <el-form-item label=\"任务组\" prop=\"taskGroup\">\n        <el-input v-model=\"form.taskGroup\" style=\"width: 220px\" />\n      </el-form-item>\n      <el-form-item label=\"程序集\" prop=\"assemblyName\">\n        <el-input v-model=\"form.assemblyName\" style=\"width: 220px\" />\n      </el-form-item>\n      <el-form-item label=\"任务名称\" prop=\"taskName\">\n        <el-input v-model=\"form.taskName\" style=\"width: 220px\" />\n      </el-form-item>\n      <el-form-item label=\"任务描述\" prop=\"description\">\n        <el-input v-model=\"form.description\" style=\"width: 220px\" />\n      </el-form-item>\n      <el-form-item label=\"任务负责人\" prop=\"principal\">\n        <el-input v-model=\"form.principal\" style=\"width: 220px\" />\n      </el-form-item>\n      <el-form-item label=\"执行类\" prop=\"className\">\n        <el-input v-model=\"form.className\" style=\"width: 220px\" />\n      </el-form-item>\n      <el-form-item label=\"作业模式\">\n        <el-radio-group v-model=\"form.triggerType\" style=\"width: 220px\" @change=\"handleRadioChange\">\n          <el-radio\n            v-for=\"item in dict.task_trigger_type\"\n            :key=\"item.id\"\n            :label=\"item.value\"\n          >\n            {{ item.label }}\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"Cron表达式\" prop=\"cron\">\n        <el-input v-model=\"form.cron\" :disabled=\"isCronDisabled\" style=\"width: 220px\" />\n      </el-form-item>\n      <el-form-item label=\"执行间隔(秒)\" prop=\"intervalSecond\">\n        <el-input\n          v-model=\"form.intervalSecond\"\n          :disabled=\"isSimpleDisabled\"\n          oninput=\"value=value.replace(/[^0-9]/g,'')\"\n          placeholder=\"请输入作业执行间隔秒数\"\n          style=\"width: 220px\"\n        />\n      </el-form-item>\n      <el-form-item label=\"循环次数\" prop=\"cycleRunTimes\">\n        <el-input\n          v-model=\"form.cycleRunTimes\"\n          :disabled=\"isSimpleDisabled\"\n          oninput=\"value=value.replace(/[^0-9]/g,'')\"\n          placeholder=\"请输入循环次数\"\n          style=\"width: 220px\"\n        />\n      </el-form-item>\n      <el-form-item label=\"开始时间\">\n        <el-date-picker\n          v-model=\"form.startTime\"\n          type=\"date\"\n          style=\"width: 220px\"\n          placeholder=\"选择日期\"\n        />\n      </el-form-item>\n      <el-form-item label=\"結束时间\">\n        <el-date-picker\n          v-model=\"form.endTime\"\n          type=\"date\"\n          style=\"width: 220px\"\n          placeholder=\"选择日期\"\n        />\n      </el-form-item>\n      <el-form-item label=\"失败后暂停\">\n        <el-radio-group v-model=\"form.pauseAfterFailure\" style=\"width: 220px\">\n          <el-radio :label=\"true\">是</el-radio>\n          <el-radio :label=\"false\">否</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"是否启用\">\n        <el-radio-group v-model=\"form.isEnable\" style=\"width: 220px\">\n          <el-radio :label=\"true\">启用</el-radio>\n          <el-radio :label=\"false\">停用</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"告警邮箱\" prop=\"alertEmail\">\n        <el-input\n          v-model=\"form.alertEmail\"\n          placeholder=\"多个邮箱用逗号隔开\"\n          style=\"width: 556px\"\n        />\n      </el-form-item>\n      <el-form-item label=\"参数内容\">\n        <el-input\n          v-model=\"form.runParams\"\n          style=\"width: 556px\"\n          rows=\"4\"\n          type=\"textarea\"\n        />\n      </el-form-item>\n    </el-form>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button type=\"text\" @click=\"crud.cancelCU\">取消</el-button>\n      <el-button\n        :loading=\"crud.status.cu === 2\"\n        type=\"primary\"\n        @click=\"crud.submitCU\"\n      >确认\n      </el-button>\n    </div>\n  </el-dialog>\n  <!--表格渲染-->\n  <el-table\n    ref=\"table\"\n    v-loading=\"crud.loading\"\n    :data=\"crud.data\"\n    style=\"width: 100%\"\n    @selection-change=\"crud.selectionChangeHandler\"\n  >\n    <el-table-column :selectable=\"checkboxT\" type=\"selection\" width=\"55\" />\n    <!--<el-table-column :show-overflow-tooltip=\"true\" prop=\"id\" label=\"任务ID\" />-->\n    <el-table-column\n      :show-overflow-tooltip=\"true\"\n      prop=\"taskName\"\n      label=\"任务名称\"\n    />\n    <el-table-column\n      :show-overflow-tooltip=\"true\"\n      prop=\"description\"\n      width=\"150px\"\n      label=\"描述\"\n    />\n    <el-table-column\n      :show-overflow-tooltip=\"true\"\n      prop=\"taskGroup\"\n      label=\"任务组\"\n    />\n    <el-table-column\n      :show-overflow-tooltip=\"true\"\n      prop=\"assemblyName\"\n      label=\"程序集\"\n    />\n    <el-table-column\n      :show-overflow-tooltip=\"true\"\n      prop=\"className\"\n      label=\"执行类\"\n    />\n    <el-table-column\n      :show-overflow-tooltip=\"true\"\n      prop=\"triggerType\"\n      label=\"触发器模式\"\n    >\n      <template slot-scope=\"scope\">\n        <el-tag type=\"success\">\n          {{ getDictText(scope.row.triggerType) }}\n        </el-tag>\n      </template>\n    </el-table-column>\n    <el-table-column\n      prop=\"cron\"\n      label=\"cron表达式\"\n    />\n    <el-table-column\n      prop=\"intervalSecond\"\n      label=\"执行间隔(秒)\"\n    />\n    <el-table-column\n      prop=\"runTimes\"\n      label=\"已执行次数\"\n    />\n    <el-table-column\n      :show-overflow-tooltip=\"true\"\n      prop=\"runParams\"\n      label=\"执行参数\"\n    />\n    <el-table-column\n      prop=\"principal\"\n      label=\"任务负责人\"\n    />\n    <el-table-column\n      :show-overflow-tooltip=\"true\"\n      prop=\"alertEmail\"\n      label=\"告警邮箱\"\n    />\n    <el-table-column\n      prop=\"isEnable\"\n      width=\"90px\"\n      label=\"DB状态\"\n    >\n      <template slot-scope=\"scope\">\n        <el-tag :type=\"scope.row.isEnable ? 'success' : 'warning'\">{{\n          scope.row.isEnable ? '启动' : '停用'\n        }}\n        </el-tag>\n      </template>\n    </el-table-column>\n    <el-table-column\n      prop=\"triggerStatus\"\n      width=\"90px\"\n      label=\"RAM状态\"\n    >\n      <template slot-scope=\"scope\">\n        <el-tag\n          :type=\"scope.row.triggerStatus === '运行中' ? 'success' : 'warning'\"\n        >{{ scope.row.triggerStatus }}\n        </el-tag>\n      </template>\n    </el-table-column>\n    <el-table-column\n      prop=\"createTime\"\n      width=\"136px\"\n      label=\"创建时间\"\n    />\n    <el-table-column\n      prop=\"updateTime\"\n      width=\"136px\"\n      label=\"更新时间\"\n    />\n    <el-table-column\n      prop=\"startTime\"\n      width=\"136px\"\n      label=\"开始时间\"\n    />\n    <el-table-column\n      prop=\"endTime\"\n      width=\"136px\"\n      label=\"结束时间\"\n    />\n    <el-table-column\n      v-if=\"checkPer(['timing_edit', 'timing_del'])\"\n      label=\"操作\"\n      width=\"170px\"\n      align=\"center\"\n      fixed=\"right\"\n    >\n      <template slot-scope=\"scope\">\n        <el-button\n          v-permission=\"['timing_edit']\"\n          size=\"mini\"\n          style=\"margin-right: 3px\"\n          type=\"text\"\n          @click=\"crud.toEdit(scope.row)\"\n        >编辑\n        </el-button>\n        <el-button\n          v-show=\"scope.row.triggerStatus==='未执行'\"\n          v-permission=\"['timing_edit']\"\n          style=\"margin-left: -2px\"\n          type=\"text\"\n          size=\"mini\"\n          @click=\"execute(scope.row.id)\"\n        >执行\n        </el-button>\n        <el-button\n          v-show=\"scope.row.triggerStatus==='运行中'\"\n          v-permission=\"['timing_edit']\"\n          style=\"margin-left: -2px\"\n          type=\"text\"\n          size=\"mini\"\n          @click=\"Pause(scope.row.id)\"\n        >暂停\n        </el-button>\n        <el-button\n          v-show=\"scope.row.triggerStatus==='暂停' \"\n          v-permission=\"['timing_edit']\"\n          style=\"margin-left: -2px\"\n          type=\"text\"\n          size=\"mini\"\n          @click=\"Resume(scope.row.id)\"\n        >恢复\n        </el-button>\n        <el-popover\n          :ref=\"scope.row.id\"\n          v-permission=\"['timing_del']\"\n          placement=\"top\"\n          width=\"200\"\n        >\n          <p>确定删除该任务和作业吗？</p>\n          <div style=\"text-align: right; margin: 0\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              @click=\"$refs[scope.row.id].doClose()\"\n            >取消\n            </el-button>\n            <el-button\n              :loading=\"delLoading\"\n              type=\"primary\"\n              size=\"mini\"\n              @click=\"delMethod(scope.row.id)\"\n            >确定\n            </el-button>\n          </div>\n          <el-button slot=\"reference\" type=\"text\" size=\"mini\">删除</el-button>\n        </el-popover>\n        <el-button\n          v-permission=\"['admin']\"\n          style=\"margin-left: -2px\"\n          type=\"text\"\n          size=\"mini\"\n          @click=\"doLog(scope.row.id)\"\n        >日志\n        </el-button>\n      </template>\n    </el-table-column>\n  </el-table>\n  <!--分页组件-->\n  <pagination />\n</div>\n", null]}