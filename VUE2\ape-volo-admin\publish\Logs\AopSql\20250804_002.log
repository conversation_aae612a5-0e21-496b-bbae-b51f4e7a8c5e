﻿
时间:2025-08-04 15:06:45.394
所在类:
等级:Warning
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAdminNotAuthentication [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:22.709ms,请检查并进行优化！


时间:2025-08-04 15:06:45.449
所在类:
等级:Warning
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  `a`.`url` AS `Url` , `a`.`method` AS `Method`  FROM `sys_user_role` `ur`  ,`sys_role_apis`  `ra` ,`sys_apis`  `a`  WHERE (( `ur`.`role_id` = `ra`.`role_id` ) AND ( `ra`.`apis_id` = `a`.`id` ))  AND ( `ur`.`user_id` = @UserId0 )  AND ( `a`.`is_deleted` = @IsDeleted1 )GROUP BY `a`.`url`,`a`.`method` ORDER BY `a`.`url` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:16.6399ms,请检查并进行优化！


时间:2025-08-04 15:06:48.032
所在类:
等级:Warning
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_id`,`label`,`value`,`dict_sort`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict_detail`  WHERE ( `dict_id` = @DictId0 )  AND ( `is_deleted` = @IsDeleted1 )ORDER BY `dict_sort` ASC  
[Pars]:
[Name]:@DictId0 [Value]:163519427764331 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:17.8826ms,请检查并进行优化！


时间:2025-08-04 15:08:50.108
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:5.6182ms


时间:2025-08-04 15:08:50.156
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.348ms


时间:2025-08-04 15:08:50.161
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_menu`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3195ms


时间:2025-08-04 15:08:50.166
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_department`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3574ms


时间:2025-08-04 15:08:50.171
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_job`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.303ms


时间:2025-08-04 15:08:50.177
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_setting`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3582ms


时间:2025-08-04 15:08:50.183
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_dict`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4038ms


时间:2025-08-04 15:08:50.188
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_dict_detail`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2938ms


时间:2025-08-04 15:08:50.194
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_quartz_job`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4606ms


时间:2025-08-04 15:08:50.201
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `email_account`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3056ms


时间:2025-08-04 15:08:50.206
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `email_message_template`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3031ms


时间:2025-08-04 15:08:50.212
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user_role`     LIMIT 0,1
[耗时]:0.3807ms


时间:2025-08-04 15:08:50.219
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user_job`     LIMIT 0,1
[耗时]:0.3376ms


时间:2025-08-04 15:08:50.224
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role_menu`     LIMIT 0,1
[耗时]:0.2767ms


时间:2025-08-04 15:08:50.233
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_apis`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3901ms


时间:2025-08-04 15:08:50.237
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role_apis`     LIMIT 0,1
[耗时]:0.3325ms


时间:2025-08-04 15:08:50.244
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_tenant`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3509ms


时间:2025-08-04 15:08:50.971
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `task_name`,`task_group`,`cron`,`assembly_name`,`class_name`,`description`,`principal`,`alert_email`,`pause_after_failure`,`run_times`,`start_time`,`end_time`,`trigger_type`,`interval_second`,`cycle_run_times`,`is_enable`,`run_params`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_quartz_job`  WHERE ( `is_deleted` = @IsDeleted0 )  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3428ms


时间:2025-08-04 15:09:00.931
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `access_token`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_token_blacklist`   WHERE ( `access_token` = @AccessToken0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@AccessToken0 [Value]:56abb002b6e99683 [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.493ms


时间:2025-08-04 15:09:00.952
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:1.6872ms


时间:2025-08-04 15:09:01.006
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:09:01.034
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:09:01.038
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:09:01.045
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:09:01.049
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:09:01.249
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAdminNotAuthentication [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3848ms


时间:2025-08-04 15:09:01.331
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  `a`.`url` AS `Url` , `a`.`method` AS `Method`  FROM `sys_user_role` `ur`  ,`sys_role_apis`  `ra` ,`sys_apis`  `a`  WHERE (( `ur`.`role_id` = `ra`.`role_id` ) AND ( `ra`.`apis_id` = `a`.`id` ))  AND ( `ur`.`user_id` = @UserId0 )  AND ( `a`.`is_deleted` = @IsDeleted1 )GROUP BY `a`.`url`,`a`.`method` ORDER BY `a`.`url` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:2.5908ms


时间:2025-08-04 15:09:01.621
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.7602ms


时间:2025-08-04 15:09:01.632
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5483ms


时间:2025-08-04 15:09:01.685
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAuditLogSaveDB [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3882ms


时间:2025-08-04 15:09:24.485
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3866ms


时间:2025-08-04 15:09:24.492
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3987ms


时间:2025-08-04 15:09:52.423
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.6246ms


时间:2025-08-04 15:09:52.433
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:09:52.438
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:09:52.449
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:09:52.463
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:09:52.467
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:09:52.488
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `m`.`permission` FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND ((( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 )) AND ( `m`.`permission` IS NOT NULL ))  AND ( `m`.`is_deleted` = @IsDeleted3 )GROUP BY `m`.`permission` ORDER BY `m`.`permission` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@Type1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[耗时]:0.5945ms


时间:2025-08-04 15:09:52.734
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  DISTINCT  `m`.`title` AS `Title` , `m`.`path` AS `Path` , `m`.`permission` AS `Permission` , `m`.`i_frame` AS `IFrame` , `m`.`component` AS `Component` , `m`.`component_name` AS `ComponentName` , `m`.`parent_id` AS `ParentId` , `m`.`sort` AS `Sort` , `m`.`icon` AS `Icon` , `m`.`type` AS `Type` , `m`.`is_deleted` AS `IsDeleted` , `m`.`id` AS `Id` , `m`.`create_time` AS `CreateTime` , `m`.`create_by` AS `CreateBy` , `m`.`cache` AS `Cache` , `m`.`hidden` AS `Hidden`  FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND (( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 ))  AND ( `m`.`is_deleted` = @IsDeleted2 )ORDER BY `m`.`sort` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@Type1 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[耗时]:0.4567ms


时间:2025-08-04 15:09:54.281
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3426ms


时间:2025-08-04 15:09:54.305
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3777ms


时间:2025-08-04 15:09:54.324
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:dept_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3464ms


时间:2025-08-04 15:09:54.339
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_id`,`label`,`value`,`dict_sort`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict_detail`  WHERE ( `dict_id` = @DictId0 )  AND ( `is_deleted` = @IsDeleted1 )ORDER BY `dict_sort` ASC  
[Pars]:
[Name]:@DictId0 [Value]:163519427764331 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3438ms


时间:2025-08-04 15:11:35.986
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3903ms


时间:2025-08-04 15:11:35.994
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_id`,`label`,`value`,`dict_sort`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict_detail`  WHERE ( `dict_id` = @DictId0 )  AND ( `is_deleted` = @IsDeleted1 )ORDER BY `dict_sort` ASC  
[Pars]:
[Name]:@DictId0 [Value]:163519427764334 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.374ms


时间:2025-08-04 15:11:36.230
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3295ms


时间:2025-08-04 15:11:36.237
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3901ms


时间:2025-08-04 15:11:36.242
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:1.6629ms


时间:2025-08-04 15:11:36.247
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3538ms


时间:2025-08-04 15:11:36.258
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:11:36.262
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:11:36.265
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:11:36.275
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:11:36.278
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:11:36.378
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:11:42.340
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `name` LIKE @Conditname0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditname0 [Value]:%信% [Type]:String    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4464ms


时间:2025-08-04 15:11:42.347
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `name` LIKE @Conditname0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditname0 [Value]:%信% [Type]:String    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4266ms


时间:2025-08-04 15:13:13.364
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3339ms


时间:2025-08-04 15:13:13.369
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:13:13.373
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:13:13.376
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:13.382
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:13:13.385
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:18.992
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3006ms


时间:2025-08-04 15:13:18.996
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:13:19.000
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:13:19.005
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:19.009
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:13:19.012
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:22.044
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4078ms


时间:2025-08-04 15:13:22.148
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5045ms


时间:2025-08-04 15:13:22.157
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3352ms


时间:2025-08-04 15:13:22.161
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:13:22.165
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:13:22.168
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:22.172
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:13:22.175
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:22.180
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:22.481
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3896ms


时间:2025-08-04 15:13:22.485
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3469ms


时间:2025-08-04 15:13:23.169
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3929ms


时间:2025-08-04 15:13:23.172
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4189ms


时间:2025-08-04 15:13:23.180
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2973ms


时间:2025-08-04 15:13:23.184
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:13:23.188
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:13:23.191
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:23.201
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:13:23.205
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:23.211
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:13:23.545
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.348ms


时间:2025-08-04 15:13:23.549
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.36ms


时间:2025-08-04 15:14:00.576
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4326ms


时间:2025-08-04 15:14:00.584
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:14:00.590
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:14:00.601
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:00.607
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:1.4261ms


时间:2025-08-04 15:14:00.608
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:14:00.611
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:14:00.620
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:00.622
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:14:00.629
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:00.636
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:14:00.640
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:06.108
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.5151ms


时间:2025-08-04 15:14:06.112
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.8979ms


时间:2025-08-04 15:14:06.117
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3408ms


时间:2025-08-04 15:14:06.139
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:14:06.144
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:14:06.158
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:06.165
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:14:06.169
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:06.179
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:09.384
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3682ms


时间:2025-08-04 15:14:09.419
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3495ms


时间:2025-08-04 15:14:09.427
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3888ms


时间:2025-08-04 15:14:09.434
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:14:09.441
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:14:09.451
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:09.460
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:14:09.467
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:09.475
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:11.512
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3625ms


时间:2025-08-04 15:14:11.525
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:14:11.531
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:14:11.537
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:11.545
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:14:11.552
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:13.427
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3454ms


时间:2025-08-04 15:14:13.432
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:6.1436ms


时间:2025-08-04 15:14:13.432
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3391ms


时间:2025-08-04 15:14:13.440
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:14:13.447
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:14:13.450
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:13.454
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:14:13.461
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:13.466
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:13.592
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5114ms


时间:2025-08-04 15:14:13.599
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4768ms


时间:2025-08-04 15:14:15.806
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3436ms


时间:2025-08-04 15:14:15.813
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:14:15.821
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:14:15.826
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:15.835
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:14:15.840
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:17.557
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3582ms


时间:2025-08-04 15:14:17.576
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3241ms


时间:2025-08-04 15:14:17.583
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3421ms


时间:2025-08-04 15:14:17.587
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:14:17.590
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:14:17.596
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:17.604
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:14:17.609
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:17.616
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:14:18.478
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.439ms


时间:2025-08-04 15:14:18.486
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4059ms


时间:2025-08-04 15:16:10.871
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3024ms


时间:2025-08-04 15:16:10.886
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:10.906
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:10.913
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:10.920
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:10.925
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:11.421
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.362ms


时间:2025-08-04 15:16:11.426
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:11.434
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:11.437
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:11.442
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:11.447
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:13.744
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.5366ms


时间:2025-08-04 15:16:13.749
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5705ms


时间:2025-08-04 15:16:13.758
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3695ms


时间:2025-08-04 15:16:13.767
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:13.771
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:16:13.779
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:13.787
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:16:13.791
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:13.796
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:14.507
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:1.973ms


时间:2025-08-04 15:16:14.514
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3245ms


时间:2025-08-04 15:16:14.523
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3692ms


时间:2025-08-04 15:16:14.529
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:14.532
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:16:14.537
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:14.545
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:16:14.548
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:14.556
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:16.822
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3344ms


时间:2025-08-04 15:16:16.851
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3187ms


时间:2025-08-04 15:16:16.891
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2946ms


时间:2025-08-04 15:16:16.903
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2906ms


时间:2025-08-04 15:16:29.541
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4676ms


时间:2025-08-04 15:16:29.546
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.7142ms


时间:2025-08-04 15:16:29.549
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:29.560
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:29.566
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:29.570
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:29.575
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:29.579
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:29.582
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:29.587
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:29.591
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:29.593
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:32.423
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3691ms


时间:2025-08-04 15:16:32.426
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3256ms


时间:2025-08-04 15:16:32.436
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3712ms


时间:2025-08-04 15:16:32.449
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:32.453
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:16:32.460
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:32.465
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:16:32.468
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:32.473
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:32.548
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4031ms


时间:2025-08-04 15:16:32.556
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4002ms


时间:2025-08-04 15:16:33.183
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3541ms


时间:2025-08-04 15:16:33.215
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3125ms


时间:2025-08-04 15:16:33.223
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2705ms


时间:2025-08-04 15:16:33.232
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:33.235
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:16:33.242
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:33.247
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:16:33.250
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:33.259
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:33.479
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3361ms


时间:2025-08-04 15:16:33.484
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3344ms


时间:2025-08-04 15:16:58.275
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3591ms


时间:2025-08-04 15:16:58.283
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:58.288
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:58.291
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:58.299
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:58.306
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:58.323
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3429ms


时间:2025-08-04 15:16:58.338
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:16:58.343
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:58.352
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:16:58.364
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:16:58.368
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:01.774
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.385ms


时间:2025-08-04 15:17:01.803
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3548ms


时间:2025-08-04 15:17:01.817
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3115ms


时间:2025-08-04 15:17:01.832
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:01.835
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.6797ms


时间:2025-08-04 15:17:01.841
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:2.1674ms


时间:2025-08-04 15:17:01.842
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:01.848
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:2.1064ms


时间:2025-08-04 15:17:01.851
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:01.853
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:01.860
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:01.866
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:01.867
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:01.869
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:01.875
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:01.877
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:01.881
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:01.890
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:02.676
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.6324ms


时间:2025-08-04 15:17:02.683
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3516ms


时间:2025-08-04 15:17:02.757
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4289ms


时间:2025-08-04 15:17:02.763
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3916ms


时间:2025-08-04 15:17:16.595
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4042ms


时间:2025-08-04 15:17:16.603
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:16.607
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:16.610
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:16.624
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:16.627
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:17.065
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3882ms


时间:2025-08-04 15:17:17.070
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:17.074
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:17.077
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:17.085
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:17.090
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:19.368
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.343ms


时间:2025-08-04 15:17:19.372
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3161ms


时间:2025-08-04 15:17:19.385
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:1.7191ms


时间:2025-08-04 15:17:19.394
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:19.402
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:19.406
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:19.416
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:19.420
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:19.426
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:19.560
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3536ms


时间:2025-08-04 15:17:19.567
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5132ms


时间:2025-08-04 15:17:20.156
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4725ms


时间:2025-08-04 15:17:20.161
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3864ms


时间:2025-08-04 15:17:20.169
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:20.172
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3321ms


时间:2025-08-04 15:17:20.173
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:20.185
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:20.195
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:20.201
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:20.208
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:20.486
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.344ms


时间:2025-08-04 15:17:20.493
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3696ms


时间:2025-08-04 15:17:37.672
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3537ms


时间:2025-08-04 15:17:37.678
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:37.682
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:37.685
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:37.693
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:37.697
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:37.994
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3466ms


时间:2025-08-04 15:17:38.002
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:38.005
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:38.009
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:38.014
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:38.018
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:40.481
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.9255ms


时间:2025-08-04 15:17:40.495
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3311ms


时间:2025-08-04 15:17:40.502
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.344ms


时间:2025-08-04 15:17:40.508
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:40.511
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:40.515
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:40.525
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:40.528
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:40.532
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:41.086
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2873ms


时间:2025-08-04 15:17:41.087
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2889ms


时间:2025-08-04 15:17:41.093
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3657ms


时间:2025-08-04 15:17:41.097
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3369ms


时间:2025-08-04 15:17:41.102
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3479ms


时间:2025-08-04 15:17:41.104
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:41.121
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:41.134
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:41.138
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:41.140
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:41.153
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:41.876
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2992ms


时间:2025-08-04 15:17:41.882
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:1.1993ms


时间:2025-08-04 15:17:54.342
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4692ms


时间:2025-08-04 15:17:54.347
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:54.353
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:54.357
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:54.362
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:54.366
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:54.447
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.31ms


时间:2025-08-04 15:17:54.452
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:54.457
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:54.462
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:54.466
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:17:54.474
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:56.794
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.35ms


时间:2025-08-04 15:17:56.796
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5211ms


时间:2025-08-04 15:17:56.806
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3546ms


时间:2025-08-04 15:17:56.811
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:56.820
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:56.823
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:56.827
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:56.835
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:56.839
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:57.465
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4164ms


时间:2025-08-04 15:17:57.470
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3501ms


时间:2025-08-04 15:17:57.982
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4171ms


时间:2025-08-04 15:17:57.986
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3532ms


时间:2025-08-04 15:17:57.988
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3817ms


时间:2025-08-04 15:17:57.999
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:17:58.009
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:58.013
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:58.022
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:17:58.026
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:58.030
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:17:58.468
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3701ms


时间:2025-08-04 15:17:58.475
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3623ms


时间:2025-08-04 15:18:10.883
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3387ms


时间:2025-08-04 15:18:10.886
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3267ms


时间:2025-08-04 15:18:10.888
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:18:10.891
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:18:10.893
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:18:10.899
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:18:10.900
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:10.902
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:10.905
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:18:10.909
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:18:10.913
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:10.916
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:13.101
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3557ms


时间:2025-08-04 15:18:13.112
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3321ms


时间:2025-08-04 15:18:13.120
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3415ms


时间:2025-08-04 15:18:13.125
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:18:13.133
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:18:13.137
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:13.143
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:18:13.151
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:13.158
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:13.551
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4133ms


时间:2025-08-04 15:18:13.555
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3249ms


时间:2025-08-04 15:18:13.728
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.339ms


时间:2025-08-04 15:18:13.794
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4356ms


时间:2025-08-04 15:18:13.802
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.7737ms


时间:2025-08-04 15:18:13.807
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:18:13.811
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:18:13.816
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:13.821
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:18:13.826
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:13.829
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:14.458
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3636ms


时间:2025-08-04 15:18:14.462
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2997ms


时间:2025-08-04 15:18:27.086
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3779ms


时间:2025-08-04 15:18:27.091
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:18:27.096
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:18:27.101
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:27.105
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:18:27.111
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:27.212
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.349ms


时间:2025-08-04 15:18:27.217
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:18:27.222
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:18:27.225
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:27.229
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:18:27.233
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:29.868
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3571ms


时间:2025-08-04 15:18:29.872
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3565ms


时间:2025-08-04 15:18:29.878
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3931ms


时间:2025-08-04 15:18:29.886
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:18:29.889
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:18:29.894
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:29.901
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:18:29.905
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:29.910
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:30.288
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:user_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3828ms


时间:2025-08-04 15:18:30.326
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`  WHERE ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:1.5643ms


时间:2025-08-04 15:18:30.338
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )  ORDER BY id desc LIMIT 0,10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.482ms


时间:2025-08-04 15:18:30.347
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:18:30.357
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:18:30.361
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764302,163519427764303)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:30.370
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` IN (163519427764301,163519427764300)    
[耗时]:0ms


时间:2025-08-04 15:18:30.378
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:30.447
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `tenant_id` AS `TenantId`,`name` AS `Name`,`description` AS `Description`,`tenant_type` AS `TenantType`,`config_id` AS `ConfigId`,`db_type` AS `DbType`,`connection_string` AS `ConnectionString`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_tenant`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `tenant_id` IN (1001,0)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:18:30.526
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3088ms


时间:2025-08-04 15:18:30.533
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2948ms


时间:2025-08-04 15:18:30.976
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3256ms


时间:2025-08-04 15:18:30.981
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3048ms


时间:2025-08-04 15:22:35.896
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3913ms


时间:2025-08-04 15:22:35.900
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:22:35.904
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:22:35.907
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:22:35.916
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:22:35.919
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 16:43:06.405
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAdminNotAuthentication [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3646ms


时间:2025-08-04 16:43:06.430
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  `a`.`url` AS `Url` , `a`.`method` AS `Method`  FROM `sys_user_role` `ur`  ,`sys_role_apis`  `ra` ,`sys_apis`  `a`  WHERE (( `ur`.`role_id` = `ra`.`role_id` ) AND ( `ra`.`apis_id` = `a`.`id` ))  AND ( `ur`.`user_id` = @UserId0 )  AND ( `a`.`is_deleted` = @IsDeleted1 )GROUP BY `a`.`url`,`a`.`method` ORDER BY `a`.`url` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:1.5854ms


时间:2025-08-04 16:43:06.480
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3262ms


时间:2025-08-04 16:43:06.500
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 16:43:06.517
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 16:43:06.521
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 16:43:06.534
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 16:43:06.548
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 16:43:06.560
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `m`.`permission` FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND ((( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 )) AND ( `m`.`permission` IS NOT NULL ))  AND ( `m`.`is_deleted` = @IsDeleted3 )GROUP BY `m`.`permission` ORDER BY `m`.`permission` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@Type1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[耗时]:3.6594ms


时间:2025-08-04 16:43:06.624
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAuditLogSaveDB [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.2941ms

