{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\utils\\echartsOptimization.js", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\utils\\echartsOptimization.js", "mtime": 1754292268389}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\babel.config.js", "mtime": 1754278696306}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754288923956}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js", "mtime": 1754288859365}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/interopRequireWildcard.js\");\nvar _interopRequireDefault = require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = exports.THEME_COLORS = exports.EChartsOptimizationMixin = exports.ECHARTS_PERFORMANCE_CONFIG = exports.COMMON_CHART_OPTIONS = exports.BIG_DATA_CHART_OPTIONS = void 0;\nexports.disposeChart = _disposeChart;\nexports.initChart = _initChart;\nexports.initGlobalEChartsOptimization = initGlobalEChartsOptimization;\nexports.setChartOption = _setChartOption;\nexports.setupChartResize = setupChartResize;\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nrequire(\"core-js/modules/es6.function.name\");\nvar echarts = _interopRequireWildcard(require(\"echarts\"));\n// Vue 2 + ECharts 4.x 性能优化配置\n\n// ECharts 4.x 性能优化配置\nvar ECHARTS_PERFORMANCE_CONFIG = exports.ECHARTS_PERFORMANCE_CONFIG = {\n  // 渲染器配置\n  renderer: 'canvas',\n  // ECharts 4.x 默认使用 canvas\n  useDirtyRect: false,\n  // ECharts 4.x 不支持脏矩形优化\n\n  // 动画配置\n  animation: true,\n  animationThreshold: 2000,\n  // 图形数量阈值\n  animationDuration: 1000,\n  animationEasing: 'cubicOut',\n  animationDelay: 0,\n  animationDurationUpdate: 300,\n  animationEasingUpdate: 'cubicOut',\n  animationDelayUpdate: 0\n};\n\n// 通用图表配置\nvar COMMON_CHART_OPTIONS = exports.COMMON_CHART_OPTIONS = {\n  // 工具提示配置\n  tooltip: {\n    trigger: 'axis',\n    backgroundColor: 'rgba(50,50,50,0.7)',\n    borderColor: '#333',\n    textStyle: {\n      color: '#fff'\n    },\n    // 性能优化：减少tooltip的渲染频率\n    enterable: false,\n    hideDelay: 100\n  },\n  // 图例配置\n  legend: {\n    type: 'scroll',\n    // 使用滚动图例\n    pageButtonItemGap: 5,\n    pageButtonGap: 20,\n    pageButtonPosition: 'end'\n  },\n  // 网格配置\n  grid: {\n    left: '3%',\n    right: '4%',\n    bottom: '3%',\n    containLabel: true\n  }\n};\n\n// 大数据量图表优化配置\nvar BIG_DATA_CHART_OPTIONS = exports.BIG_DATA_CHART_OPTIONS = {\n  // 采样配置\n  sampling: 'average',\n  // ECharts 4.x 支持的采样方法\n\n  // 大数据渲染优化\n  large: true,\n  largeThreshold: 2000,\n  // 动画优化\n  animation: false,\n  // 大数据时关闭动画\n\n  // 工具提示优化\n  tooltip: {\n    trigger: 'axis',\n    axisPointer: {\n      type: 'cross'\n    },\n    // 大数据时简化tooltip内容\n    formatter: function formatter(params) {\n      if (Array.isArray(params) && params.length > 10) {\n        return \"\".concat(params[0].name, \": \").concat(params.length, \" \\u4E2A\\u6570\\u636E\\u70B9\");\n      }\n      return params.map(function (p) {\n        return \"\".concat(p.seriesName, \": \").concat(p.value);\n      }).join('<br/>');\n    }\n  }\n};\n\n// 主题色彩配置\nvar THEME_COLORS = exports.THEME_COLORS = {\n  primary: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],\n  dark: ['#dd6b66', '#759aa0', '#e69d87', '#8dc1a9', '#ea7e53', '#eedd78', '#73a373', '#73b9bc', '#7289ab'],\n  light: ['#37A2DA', '#32C5E9', '#67E0E3', '#9FE6B8', '#FFDB5C', '#ff9f7f', '#fb7293', '#E062AE', '#E690D1']\n};\n\n// 初始化ECharts实例的工具函数\nfunction _initChart(container) {\n  var theme = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'macarons';\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!container) {\n    console.warn('ECharts container is null');\n    return null;\n  }\n\n  // 检查容器尺寸\n  var rect = container.getBoundingClientRect();\n  if (rect.width === 0 || rect.height === 0) {\n    console.warn('ECharts container has no size:', rect);\n    return null;\n  }\n  try {\n    // 合并性能配置\n    var config = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, ECHARTS_PERFORMANCE_CONFIG), options);\n    return echarts.init(container, theme, config);\n  } catch (error) {\n    console.error('Failed to initialize ECharts:', error);\n    return null;\n  }\n}\n\n// 设置图表选项的工具函数\nfunction _setChartOption(chartInstance, option) {\n  var notMerge = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var lazyUpdate = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (!chartInstance || !option) {\n    console.warn('Invalid chart instance or option');\n    return;\n  }\n  try {\n    // 合并通用配置\n    var mergedOption = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, COMMON_CHART_OPTIONS), option);\n    chartInstance.setOption(mergedOption, notMerge, lazyUpdate);\n  } catch (error) {\n    console.error('Failed to set chart option:', error);\n  }\n}\n\n// 响应式图表大小调整（Vue 2 兼容）\nfunction setupChartResize(chartInstance, container) {\n  if (!chartInstance || !container) return;\n\n  // Vue 2 环境下的resize处理\n  var handleResize = function handleResize() {\n    // 使用 setTimeout 来避免频繁调用\n    setTimeout(function () {\n      if (chartInstance && !chartInstance.isDisposed()) {\n        chartInstance.resize();\n      }\n    }, 100);\n  };\n\n  // 监听窗口大小变化\n  window.addEventListener('resize', handleResize);\n  return function () {\n    window.removeEventListener('resize', handleResize);\n  };\n}\n\n// 销毁图表实例\nfunction _disposeChart(chartInstance) {\n  if (chartInstance && !chartInstance.isDisposed()) {\n    chartInstance.dispose();\n  }\n}\n\n// Vue 2 Mixin for ECharts optimization\nvar EChartsOptimizationMixin = exports.EChartsOptimizationMixin = {\n  data: function data() {\n    return {\n      chartInstance: null,\n      resizeHandler: null\n    };\n  },\n  methods: {\n    // 初始化图表\n    initChart: function initChart(container) {\n      var theme = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'macarons';\n      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.chartInstance = _initChart(container, theme, options);\n      if (this.chartInstance) {\n        // 设置响应式\n        this.resizeHandler = setupChartResize(this.chartInstance, container);\n      }\n      return this.chartInstance;\n    },\n    // 设置图表选项\n    setChartOption: function setChartOption(option) {\n      var notMerge = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var lazyUpdate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      _setChartOption(this.chartInstance, option, notMerge, lazyUpdate);\n    },\n    // 销毁图表\n    disposeChart: function disposeChart() {\n      if (this.resizeHandler) {\n        this.resizeHandler();\n        this.resizeHandler = null;\n      }\n      _disposeChart(this.chartInstance);\n      this.chartInstance = null;\n    }\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 组件销毁时自动清理图表\n    this.disposeChart();\n  }\n};\n\n// 全局ECharts优化初始化\nfunction initGlobalEChartsOptimization() {\n  // 注册自定义主题\n  echarts.registerTheme('optimized', {\n    color: THEME_COLORS.primary,\n    backgroundColor: 'rgba(0,0,0,0)',\n    textStyle: {},\n    title: {\n      textStyle: {\n        color: '#516b91'\n      },\n      subtextStyle: {\n        color: '#93b7e3'\n      }\n    },\n    line: {\n      itemStyle: {\n        borderWidth: 1\n      },\n      lineStyle: {\n        width: 2\n      },\n      symbolSize: 4,\n      symbol: 'emptyCircle',\n      smooth: false\n    },\n    radar: {\n      itemStyle: {\n        borderWidth: 1\n      },\n      lineStyle: {\n        width: 2\n      },\n      symbolSize: 4,\n      symbol: 'emptyCircle',\n      smooth: false\n    },\n    bar: {\n      itemStyle: {\n        barBorderWidth: 0,\n        barBorderColor: '#ccc'\n      }\n    },\n    pie: {\n      itemStyle: {\n        borderWidth: 0,\n        borderColor: '#ccc'\n      }\n    },\n    scatter: {\n      itemStyle: {\n        borderWidth: 0,\n        borderColor: '#ccc'\n      }\n    },\n    boxplot: {\n      itemStyle: {\n        borderWidth: 0,\n        borderColor: '#ccc'\n      }\n    },\n    parallel: {\n      itemStyle: {\n        borderWidth: 0,\n        borderColor: '#ccc'\n      }\n    },\n    sankey: {\n      itemStyle: {\n        borderWidth: 0,\n        borderColor: '#ccc'\n      }\n    },\n    funnel: {\n      itemStyle: {\n        borderWidth: 0,\n        borderColor: '#ccc'\n      }\n    },\n    gauge: {\n      itemStyle: {\n        borderWidth: 0,\n        borderColor: '#ccc'\n      }\n    },\n    candlestick: {\n      itemStyle: {\n        color: '#fd1050',\n        color0: '#0cf49b',\n        borderColor: '#fd1050',\n        borderColor0: '#0cf49b',\n        borderWidth: 1\n      }\n    },\n    graph: {\n      itemStyle: {\n        borderWidth: 0,\n        borderColor: '#ccc'\n      },\n      lineStyle: {\n        width: 1,\n        color: '#aaa'\n      },\n      symbolSize: 4,\n      symbol: 'emptyCircle',\n      smooth: false,\n      color: THEME_COLORS.primary,\n      label: {\n        color: '#eee'\n      }\n    },\n    map: {\n      itemStyle: {\n        areaColor: '#eee',\n        borderColor: '#444',\n        borderWidth: 0.5\n      },\n      label: {\n        color: '#000'\n      },\n      emphasis: {\n        itemStyle: {\n          areaColor: 'rgba(255,215,0,0.8)',\n          borderColor: '#444',\n          borderWidth: 1\n        },\n        label: {\n          color: 'rgb(100,0,0)'\n        }\n      }\n    },\n    geo: {\n      itemStyle: {\n        areaColor: '#eee',\n        borderColor: '#444',\n        borderWidth: 0.5\n      },\n      label: {\n        color: '#000'\n      },\n      emphasis: {\n        itemStyle: {\n          areaColor: 'rgba(255,215,0,0.8)',\n          borderColor: '#444',\n          borderWidth: 1\n        },\n        label: {\n          color: 'rgb(100,0,0)'\n        }\n      }\n    },\n    categoryAxis: {\n      axisLine: {\n        show: true,\n        lineStyle: {\n          color: '#6E7079'\n        }\n      },\n      axisTick: {\n        show: true,\n        lineStyle: {\n          color: '#6E7079'\n        }\n      },\n      axisLabel: {\n        show: true,\n        color: '#6E7079'\n      },\n      splitLine: {\n        show: false,\n        lineStyle: {\n          color: ['#E0E6F1']\n        }\n      },\n      splitArea: {\n        show: false,\n        areaStyle: {\n          color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']\n        }\n      }\n    },\n    valueAxis: {\n      axisLine: {\n        show: false,\n        lineStyle: {\n          color: '#6E7079'\n        }\n      },\n      axisTick: {\n        show: false,\n        lineStyle: {\n          color: '#6E7079'\n        }\n      },\n      axisLabel: {\n        show: true,\n        color: '#6E7079'\n      },\n      splitLine: {\n        show: true,\n        lineStyle: {\n          color: ['#E0E6F1']\n        }\n      },\n      splitArea: {\n        show: false,\n        areaStyle: {\n          color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']\n        }\n      }\n    },\n    logAxis: {\n      axisLine: {\n        show: false,\n        lineStyle: {\n          color: '#6E7079'\n        }\n      },\n      axisTick: {\n        show: false,\n        lineStyle: {\n          color: '#6E7079'\n        }\n      },\n      axisLabel: {\n        show: true,\n        color: '#6E7079'\n      },\n      splitLine: {\n        show: true,\n        lineStyle: {\n          color: ['#E0E6F1']\n        }\n      },\n      splitArea: {\n        show: false,\n        areaStyle: {\n          color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']\n        }\n      }\n    },\n    timeAxis: {\n      axisLine: {\n        show: true,\n        lineStyle: {\n          color: '#6E7079'\n        }\n      },\n      axisTick: {\n        show: true,\n        lineStyle: {\n          color: '#6E7079'\n        }\n      },\n      axisLabel: {\n        show: true,\n        color: '#6E7079'\n      },\n      splitLine: {\n        show: false,\n        lineStyle: {\n          color: ['#E0E6F1']\n        }\n      },\n      splitArea: {\n        show: false,\n        areaStyle: {\n          color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']\n        }\n      }\n    },\n    toolbox: {\n      iconStyle: {\n        borderColor: '#999'\n      },\n      emphasis: {\n        iconStyle: {\n          borderColor: '#666'\n        }\n      }\n    },\n    legend: {\n      textStyle: {\n        color: '#333'\n      }\n    },\n    tooltip: {\n      axisPointer: {\n        lineStyle: {\n          color: '#ccc',\n          width: 1\n        },\n        crossStyle: {\n          color: '#ccc',\n          width: 1\n        }\n      }\n    },\n    timeline: {\n      lineStyle: {\n        color: '#293c55',\n        width: 1\n      },\n      itemStyle: {\n        color: '#293c55',\n        borderWidth: 1\n      },\n      controlStyle: {\n        color: '#293c55',\n        borderColor: '#293c55',\n        borderWidth: 0.5\n      },\n      checkpointStyle: {\n        color: '#e43c59',\n        borderColor: '#c23531'\n      },\n      label: {\n        color: '#293c55'\n      },\n      emphasis: {\n        itemStyle: {\n          color: '#a9334c'\n        },\n        controlStyle: {\n          color: '#293c55',\n          borderColor: '#293c55',\n          borderWidth: 0.5\n        },\n        label: {\n          color: '#293c55'\n        }\n      }\n    },\n    visualMap: {\n      color: ['#bf444c', '#d88273', '#f6efa6']\n    },\n    dataZoom: {\n      backgroundColor: 'rgba(47,69,84,0)',\n      dataBackgroundColor: 'rgba(47,69,84,0.3)',\n      fillerColor: 'rgba(167,183,204,0.4)',\n      handleColor: '#a7b7cc',\n      handleSize: '100%',\n      textStyle: {\n        color: '#333'\n      }\n    },\n    markPoint: {\n      label: {\n        color: '#eee'\n      },\n      emphasis: {\n        label: {\n          color: '#eee'\n        }\n      }\n    }\n  });\n  console.log('Global ECharts optimization initialized for Vue 2');\n}\n\n// Vue 2 插件\nvar _default = exports.default = {\n  install: function install(Vue) {\n    // 全局混入 ECharts 优化方法\n    Vue.mixin(EChartsOptimizationMixin);\n\n    // 初始化全局优化\n    initGlobalEChartsOptimization();\n\n    // 添加全局方法\n    Vue.prototype.$echarts = echarts;\n    Vue.prototype.$initChart = _initChart;\n    Vue.prototype.$setChartOption = _setChartOption;\n    Vue.prototype.$disposeChart = _disposeChart;\n  }\n};", null]}