{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\system\\tasks\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\system\\tasks\\index.vue", "mtime": 1754292873038}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754288923956}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport crudJob from '@/api/system/tasks'\r\nimport Log from './log'\r\nimport CRUD, { presenter, header, form, crud } from '@crud/crud'\r\nimport rrOperation from '@crud/RR.operation'\r\nimport crudOperation from '@crud/CRUD.operation'\r\nimport pagination from '@crud/Pagination'\r\nimport DateRangePicker from '@/components/DateRangePicker'\r\n\r\nconst defaultForm = {\r\n  id: 0,\r\n  taskGroup: null,\r\n  assemblyName: null,\r\n  taskName: null,\r\n  description: null,\r\n  className: null,\r\n  cron: null,\r\n  principal: null,\r\n  alertEmail: null,\r\n  startTime: null,\r\n  endTime: null,\r\n  triggerType: '1',\r\n  pauseAfterFailure: true,\r\n  isEnable: false,\r\n  runParams: null,\r\n  intervalSecond: null,\r\n  cycleRunTimes: null\r\n}\r\nexport default {\r\n  name: 'Timing',\r\n  components: { Log, pagination, crudOperation, rrOperation, DateRangePicker },\r\n  cruds() {\r\n    return CRUD({\r\n      title: '作业调度',\r\n      url: 'api/tasks/query',\r\n      crudMethod: { ...crudJob }\r\n    })\r\n  },\r\n  mixins: [presenter(), header(), form(defaultForm), crud()],\r\n  dicts: ['task_trigger_type'],\r\n  data() {\r\n    return {\r\n      delLoading: false,\r\n      permission: {\r\n        add: ['timing_add'],\r\n        edit: ['timing_edit'],\r\n        del: ['timing_del'],\r\n        down: ['timing_down']\r\n      },\r\n      rules: {\r\n        taskGroup: [\r\n          { required: true, message: '请输入任务組', trigger: 'blur' }\r\n        ],\r\n        assemblyName: [\r\n          { required: true, message: '请输入程序集', trigger: 'blur' }\r\n        ],\r\n        taskName: [\r\n          { required: true, message: '请输入任务名称', trigger: 'blur' }\r\n        ],\r\n        description: [\r\n          { required: true, message: '请输入任务描述', trigger: 'blur' }\r\n        ],\r\n        className: [\r\n          { required: true, message: '请输入执行类', trigger: 'blur' }\r\n        ],\r\n        principal: [\r\n          { required: true, message: '请输入负责人名称', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    isCronDisabled() {\r\n      return this.form.triggerType === '0'\r\n    },\r\n    isSimpleDisabled() {\r\n      return this.form.triggerType === '1'\r\n    }\r\n  },\r\n  methods: {\r\n    // 执行\r\n    execute(id) {\r\n      crudJob\r\n        .execution(id)\r\n        .then((res) => {\r\n          this.crud.message('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)\r\n          this.crud.toQuery()\r\n        })\r\n        .catch((err) => {\r\n          console.log(err.response.data.message)\r\n        })\r\n    },\r\n    // 暂停\r\n    Pause(id) {\r\n      crudJob\r\n        .pause(id)\r\n        .then((res) => {\r\n          this.crud.message('暂停成功', CRUD.NOTIFICATION_TYPE.SUCCESS)\r\n          this.crud.toQuery()\r\n        })\r\n        .catch((err) => {\r\n          console.log(err.response.data.message)\r\n        })\r\n    },\r\n    // 恢复\r\n    Resume(id) {\r\n      crudJob\r\n        .resume(id)\r\n        .then((res) => {\r\n          this.crud.message('恢复成功', CRUD.NOTIFICATION_TYPE.SUCCESS)\r\n          this.crud.toQuery()\r\n        })\r\n        .catch((err) => {\r\n          console.log(err.response.data.message)\r\n        })\r\n    },\r\n    delMethod(id) {\r\n      this.delLoading = true\r\n      const idCollection = { idArray: [id] }\r\n      crudJob\r\n        .del(idCollection)\r\n        .then(() => {\r\n          this.delLoading = false\r\n          this.$refs[id].doClose()\r\n          this.crud.dleChangePage(1)\r\n          this.crud.delSuccessMessage()\r\n          this.crud.toQuery()\r\n        })\r\n        .catch(() => {\r\n          this.delLoading = false\r\n          this.$refs[id].doClose()\r\n        })\r\n    },\r\n    // 显示日志\r\n    doLog(id) {\r\n      this.$refs.log.dialog = true\r\n      this.$refs.log.doInit(id)\r\n    },\r\n    checkboxT(row, rowIndex) {\r\n      return row.id !== 1\r\n    },\r\n    getDictText(value) {\r\n      try {\r\n        const dictList = this.dict.task_trigger_type\r\n        const keys = Object.keys(dictList)\r\n        const foundKey = keys.find(key => dictList[key].value === value.toString())\r\n        if (foundKey) {\r\n          return dictList[foundKey].label\r\n        }\r\n        return value\r\n      } catch (err) {\r\n        return value\r\n      }\r\n    },\r\n    handleRadioChange() {\r\n      if (this.form.triggerType === '1') {\r\n        this.form.intervalSecond = null\r\n        this.form.cycleRunTimes = null\r\n      } else if (this.form.triggerType === '0') {\r\n        this.form.cron = null\r\n      }\r\n    },\r\n    [CRUD.HOOK.afterValidateCU](crud) {\r\n      if (crud.form.triggerType === '1') {\r\n        if (crud.form.cron === null) {\r\n          this.$message({\r\n            message: 'cron模式下请设置作业执行cron表达式',\r\n            type: 'warning',\r\n            center: true\r\n          })\r\n          return false\r\n        }\r\n      } else if (crud.form.triggerType === '0') {\r\n        if (crud.form.intervalSecond === null || crud.form.intervalSecond <= 0) {\r\n          this.$message({\r\n            message: 'simple模式下请设置作业间隔执行秒数',\r\n            type: 'warning',\r\n            center: true\r\n          })\r\n          return false\r\n        }\r\n      }\r\n      if (crud.form.triggerType === '0') {\r\n        crud.form.intervalSecond = Number(crud.form.intervalSecond)\r\n      }\r\n      crud.form.triggerType = Number(crud.form.triggerType)\r\n      return true\r\n    },\r\n    // 新增与编辑前做的操作\r\n    [CRUD.HOOK.afterToCU](crud) {\r\n      crud.form.triggerType = crud.form.triggerType.toString()\r\n    }\r\n  }\r\n}\r\n", null]}