﻿
时间:2025-08-05 09:07:03.448
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-05 09:07:03.454
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:The file "/uploads/file/avatar/20231010143458_1711631391147429888.png" was not modified

时间:2025-08-05 09:07:03.457
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 304 in 4.3594 ms

时间:2025-08-05 09:07:03.574
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 304 null "image/png" 125.5847ms

时间:2025-08-05 09:07:05.240
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-05 09:07:05.313
所在类:Microsoft.AspNetCore.Authorization.DefaultAuthorizationService
等级:Information
信息:Authorization failed. "Fail() was explicitly called."

时间:2025-08-05 09:07:05.641
所在类:Ape.Volo.Infrastructure.Authentication.ApiResponseHandler
等级:Information
信息:AuthenticationScheme: "ApiResponseHandler" was challenged.

时间:2025-08-05 09:07:05.647
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 401 in 349.7907 ms

时间:2025-08-05 09:07:05.651
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 401 null "application/json" 413.6791ms

时间:2025-08-05 09:07:05.670
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/refreshToken""" - "application/json" 368

时间:2025-08-05 09:07:05.734
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-05 09:07:05.832
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.RefreshToken (Ape.Volo.Api)"'

时间:2025-08-05 09:07:05.895
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"RefreshToken\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] RefreshToken(System.String)" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-05 09:07:05.958
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.RefreshToken (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-05 09:07:06.893
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-05 09:07:06.896
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.RefreshToken (Ape.Volo.Api)" in 995.9883ms

时间:2025-08-05 09:07:06.900
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.RefreshToken (Ape.Volo.Api)"'

时间:2025-08-05 09:07:06.908
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/refreshToken""" - 500 148 "application/json; charset=utf-8" 1238.1814ms

时间:2025-08-05 09:07:06.914
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "DELETE" "http"://"localhost:8002""""/auth/logout""" - null 0

时间:2025-08-05 09:07:06.916
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-05 09:07:06.923
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-05 09:07:06.928
所在类:Microsoft.AspNetCore.Authorization.DefaultAuthorizationService
等级:Information
信息:Authorization failed. "Fail() was explicitly called."

时间:2025-08-05 09:07:06.929
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)"'

时间:2025-08-05 09:07:06.930
所在类:Ape.Volo.Infrastructure.Authentication.ApiResponseHandler
等级:Information
信息:AuthenticationScheme: "ApiResponseHandler" was challenged.

时间:2025-08-05 09:07:06.933
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 401 in 6.1274 ms

时间:2025-08-05 09:07:06.935
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 401 null "application/json" 19.4842ms

时间:2025-08-05 09:07:06.940
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Logout\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Logout()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-05 09:07:06.947
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-05 09:07:06.957
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 8.2442ms.

时间:2025-08-05 09:07:07.157
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-05 09:07:07.159
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)" in 212.8283ms

时间:2025-08-05 09:07:07.165
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)"'

时间:2025-08-05 09:07:07.168
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "DELETE" "/auth/logout" responded 200 in 245.5111 ms

时间:2025-08-05 09:07:07.176
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "DELETE" "http"://"localhost:8002""""/auth/logout""" - 200 108 "application/json; charset=utf-8" 261.8901ms

时间:2025-08-05 09:07:07.237
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "DELETE" "http"://"localhost:8002""""/auth/logout""" - null 0

时间:2025-08-05 09:07:07.291
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-05 09:07:07.296
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)"'

时间:2025-08-05 09:07:07.301
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Logout\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Logout()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-05 09:07:07.303
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "DELETE" "http"://"localhost:8002""""/auth/logout""" - null 0

时间:2025-08-05 09:07:08.295
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - null null

时间:2025-08-05 09:07:08.554
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-05 09:07:08.574
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-05 09:07:08.625
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'
